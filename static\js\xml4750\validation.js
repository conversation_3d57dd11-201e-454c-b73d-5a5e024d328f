/**
 * XML4750 Validation Module
 * Cung cấp các chức năng kiểm tra dữ liệu XML theo cấu hình
 */

// Namespace cho module validation
window.XMLValidation = (function() {
    // Lưu trữ cấu hình kiểm tra
    let fieldConfigs = {};
    let validationRules = [];
    
    // Lưu trữ kết quả kiểm tra
    let validationResults = {};
    
    /**
     * Tải cấu hình kiểm tra từ server
     * @returns {Promise} Promise khi tải xong
     */
    function loadValidationConfig() {
        return new Promise((resolve, reject) => {
            // Sử dụng XMLValidationConfig module nếu có
            if (window.XMLValidationConfig) {
                window.XMLValidationConfig.loadAllConfigs()
                    .then(() => {
                        // Cập nhật local cache
                        fieldConfigs = {};
                        validationRules = window.XMLValidationConfig.getValidationRules();

                        // Tải field configs cho từng XML type
                        const xmlTypes = ['XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];

                        Promise.all(xmlTypes.map(xmlType =>
                            window.XMLValidationConfig.getXmlFields(xmlType)
                                .then(fields => {
                                    fields.forEach(field => {
                                        const config = window.XMLValidationConfig.getFieldConfig(xmlType, field.name);
                                        if (config) {
                                            fieldConfigs[`${xmlType}.${field.name}`] = config;
                                        }
                                    });
                                })
                                .catch(err => console.warn(`Could not load fields for ${xmlType}:`, err))
                        )).then(() => {
                            console.log('Loaded field configs:', fieldConfigs);
                            console.log('Loaded validation rules:', validationRules);
                            resolve();
                        }).catch(reject);
                    })
                    .catch(reject);
            } else {
                // Fallback to original implementation
                loadValidationConfigFallback().then(resolve).catch(reject);
            }
        });
    }

    /**
     * Fallback method để tải cấu hình (phương pháp cũ)
     */
    function loadValidationConfigFallback() {
        return new Promise((resolve, reject) => {
            // Tải cấu hình trường
            $.ajax({
                url: '/xml4750/api/get_all_field_configs/',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        fieldConfigs = response.configs;
                        console.log('Loaded field configs:', fieldConfigs);

                        // Tải quy tắc kiểm tra
                        $.ajax({
                            url: '/xml4750/api/get_validation_rules/',
                            type: 'GET',
                            success: function(response) {
                                if (response.success) {
                                    validationRules = response.rules;
                                    console.log('Loaded validation rules:', validationRules);
                                    resolve();
                                } else {
                                    console.error('Error loading validation rules:', response.message);
                                    reject(new Error(response.message));
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Error loading validation rules:', error);
                                reject(new Error(error));
                            }
                        });
                    } else {
                        console.error('Error loading field configs:', response.message);
                        reject(new Error(response.message));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading field configs:', error);
                    reject(new Error(error));
                }
            });
        });
    }
    
    /**
     * Kiểm tra một giá trị theo cấu hình trường
     * @param {string} xmlType - Loại XML (XML1, XML2, ...)
     * @param {string} fieldName - Tên trường
     * @param {any} value - Giá trị cần kiểm tra
     * @returns {Object} Kết quả kiểm tra {valid: boolean, message: string}
     */
    function validateField(xmlType, fieldName, value) {
        // Tìm cấu hình cho trường
        const configKey = `${xmlType}.${fieldName}`;
        const config = fieldConfigs[configKey];
        
        if (!config) {
            return { valid: true, message: null }; // Không có cấu hình, coi như hợp lệ
        }
        
        // Kiểm tra trường bắt buộc
        if (config.required && (value === null || value === undefined || value === '')) {
            return {
                valid: false,
                message: `Trường ${fieldName} là bắt buộc`,
                blocking: config.blocking
            };
        }
        
        // Nếu giá trị rỗng và không bắt buộc, coi như hợp lệ
        if (value === null || value === undefined || value === '') {
            return { valid: true, message: null };
        }
        
        // Kiểm tra kiểu dữ liệu
        switch (config.dataType) {
            case 'number':
                if (isNaN(parseFloat(value))) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} phải là số`,
                        blocking: config.blocking
                    };
                }
                break;
                
            case 'date':
                if (!isValidDate(value)) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} phải là ngày hợp lệ (định dạng: ${config.format || 'DD/MM/YYYY'})`,
                        blocking: config.blocking
                    };
                }
                break;
                
            case 'datetime':
                if (!isValidDateTime(value)) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} phải là ngày giờ hợp lệ (định dạng: ${config.format || 'DD/MM/YYYY HH:mm:ss'})`,
                        blocking: config.blocking
                    };
                }
                break;
                
            case 'boolean':
                if (typeof value !== 'boolean' && value !== '0' && value !== '1' && value !== 0 && value !== 1) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} phải là giá trị boolean (true/false hoặc 0/1)`,
                        blocking: config.blocking
                    };
                }
                break;
        }
        
        // Kiểm tra định dạng (nếu có)
        if (config.format && typeof value === 'string') {
            try {
                const regex = new RegExp(config.format);
                if (!regex.test(value)) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} không đúng định dạng`,
                        blocking: config.blocking
                    };
                }
            } catch (e) {
                console.error('Invalid regex format:', config.format, e);
            }
        }
        
        return { valid: true, message: null };
    }
    
    /**
     * Kiểm tra một bản ghi XML theo các quy tắc nâng cao
     * @param {Object} record - Bản ghi cần kiểm tra
     * @param {string} xmlType - Loại XML của bản ghi
     * @param {Object} allData - Tất cả dữ liệu XML (để tham chiếu chéo)
     * @returns {Array} Danh sách các lỗi {rule: Object, message: string}
     */
    function validateRecordAgainstRules(record, xmlType, allData) {
        const errors = [];
        
        // Lấy maLK từ bản ghi
        const maLK = record.maLK;
        if (!maLK) {
            return [{ message: 'Không có mã liên kết (maLK)', blocking: true }];
        }
        
        // Lọc các quy tắc liên quan đến loại XML này
        const relevantRules = validationRules.filter(rule => 
            rule.sourceXmlType === xmlType || rule.targetXmlType === xmlType
        );
        
        for (const rule of relevantRules) {
            let sourceValue, targetValue;
            let sourceRecord, targetRecord;
            
            // Xác định bản ghi nguồn và đích
            if (rule.sourceXmlType === xmlType) {
                sourceRecord = record;
                sourceValue = record[rule.sourceField];
                
                // Tìm bản ghi đích trong tất cả dữ liệu
                if (allData[rule.targetXmlType]) {
                    targetRecord = allData[rule.targetXmlType].find(r => r.maLK === maLK);
                    if (targetRecord) {
                        targetValue = targetRecord[rule.targetField];
                    }
                }
            } else if (rule.targetXmlType === xmlType) {
                targetRecord = record;
                targetValue = record[rule.targetField];
                
                // Tìm bản ghi nguồn trong tất cả dữ liệu
                if (allData[rule.sourceXmlType]) {
                    sourceRecord = allData[rule.sourceXmlType].find(r => r.maLK === maLK);
                    if (sourceRecord) {
                        sourceValue = sourceRecord[rule.sourceField];
                    }
                }
            }
            
            // Nếu không tìm thấy bản ghi nguồn hoặc đích, bỏ qua quy tắc này
            if (sourceValue === undefined || targetValue === undefined) {
                continue;
            }
            
            // Kiểm tra điều kiện
            let conditionMet = false;
            
            switch (rule.condition) {
                case 'equal':
                    conditionMet = sourceValue == targetValue;
                    break;
                case 'not_equal':
                    conditionMet = sourceValue != targetValue;
                    break;
                case 'greater':
                    conditionMet = parseFloat(sourceValue) > parseFloat(targetValue);
                    break;
                case 'greater_equal':
                    conditionMet = parseFloat(sourceValue) >= parseFloat(targetValue);
                    break;
                case 'less':
                    conditionMet = parseFloat(sourceValue) < parseFloat(targetValue);
                    break;
                case 'less_equal':
                    conditionMet = parseFloat(sourceValue) <= parseFloat(targetValue);
                    break;
                case 'contains':
                    conditionMet = String(sourceValue).includes(String(targetValue));
                    break;
                case 'not_contains':
                    conditionMet = !String(sourceValue).includes(String(targetValue));
                    break;
                case 'starts_with':
                    conditionMet = String(sourceValue).startsWith(String(targetValue));
                    break;
                case 'ends_with':
                    conditionMet = String(sourceValue).endsWith(String(targetValue));
                    break;
                case 'regex':
                    try {
                        const regex = new RegExp(targetValue);
                        conditionMet = regex.test(String(sourceValue));
                    } catch (e) {
                        console.error('Invalid regex:', targetValue, e);
                        conditionMet = false;
                    }
                    break;
                case 'custom':
                    try {
                        // Tạo hàm từ biểu thức JavaScript
                        const customFunc = new Function('sourceValue', 'targetValue', rule.customCondition);
                        conditionMet = customFunc(sourceValue, targetValue);
                    } catch (e) {
                        console.error('Error in custom condition:', rule.customCondition, e);
                        conditionMet = false;
                    }
                    break;
            }
            
            // Nếu điều kiện không thỏa mãn, thêm lỗi
            if (!conditionMet) {
                errors.push({
                    rule: rule,
                    message: rule.errorMessage,
                    blocking: rule.isBlocking
                });
            }
        }
        
        return errors;
    }
    
    /**
     * Kiểm tra tất cả dữ liệu XML
     * @param {Object} allData - Tất cả dữ liệu XML (key: loại XML, value: mảng các bản ghi)
     * @returns {Object} Kết quả kiểm tra (key: maLK, value: mảng các lỗi)
     */
    function validateAllData(allData) {
        validationResults = {};
        
        // Duyệt qua từng loại XML
        for (const xmlType in allData) {
            const records = allData[xmlType];
            
            // Duyệt qua từng bản ghi
            for (const record of records) {
                const maLK = record.maLK;
                if (!maLK) continue;
                
                // Khởi tạo kết quả cho maLK nếu chưa có
                if (!validationResults[maLK]) {
                    validationResults[maLK] = {
                        errors: [],
                        hasBlockingError: false
                    };
                }
                
                // Kiểm tra từng trường trong bản ghi
                for (const fieldName in record) {
                    const value = record[fieldName];
                    const result = validateField(xmlType, fieldName, value);
                    
                    if (!result.valid) {
                        validationResults[maLK].errors.push({
                            xmlType: xmlType,
                            fieldName: fieldName,
                            message: result.message,
                            blocking: result.blocking
                        });
                        
                        if (result.blocking) {
                            validationResults[maLK].hasBlockingError = true;
                        }
                    }
                }
                
                // Kiểm tra quy tắc nâng cao
                const ruleErrors = validateRecordAgainstRules(record, xmlType, allData);
                
                for (const error of ruleErrors) {
                    validationResults[maLK].errors.push({
                        xmlType: xmlType,
                        message: error.message,
                        blocking: error.blocking
                    });
                    
                    if (error.blocking) {
                        validationResults[maLK].hasBlockingError = true;
                    }
                }
            }
        }
        
        return validationResults;
    }
    
    /**
     * Kiểm tra một bản ghi đơn lẻ
     * @param {Object} record - Bản ghi cần kiểm tra
     * @param {string} xmlType - Loại XML của bản ghi
     * @param {Object} allData - Tất cả dữ liệu XML (để tham chiếu chéo)
     * @returns {Object} Kết quả kiểm tra {valid: boolean, errors: Array}
     */
    function validateRecord(record, xmlType, allData) {
        const errors = [];
        let hasBlockingError = false;
        
        // Kiểm tra từng trường trong bản ghi
        for (const fieldName in record) {
            const value = record[fieldName];
            const result = validateField(xmlType, fieldName, value);
            
            if (!result.valid) {
                errors.push({
                    fieldName: fieldName,
                    message: result.message,
                    blocking: result.blocking
                });
                
                if (result.blocking) {
                    hasBlockingError = true;
                }
            }
        }
        
        // Kiểm tra quy tắc nâng cao
        const ruleErrors = validateRecordAgainstRules(record, xmlType, allData);
        
        for (const error of ruleErrors) {
            errors.push({
                message: error.message,
                blocking: error.blocking
            });
            
            if (error.blocking) {
                hasBlockingError = true;
            }
        }
        
        return {
            valid: errors.length === 0,
            hasBlockingError: hasBlockingError,
            errors: errors
        };
    }
    
    /**
     * Kiểm tra xem một ngày có hợp lệ không
     * @param {string} dateStr - Chuỗi ngày cần kiểm tra
     * @returns {boolean} true nếu hợp lệ, false nếu không
     */
    function isValidDate(dateStr) {
        if (!dateStr) return false;
        
        // Hỗ trợ nhiều định dạng ngày
        const formats = [
            'DD/MM/YYYY',
            'YYYY-MM-DD',
            'YYYY/MM/DD',
            'DD-MM-YYYY'
        ];
        
        for (const format of formats) {
            const date = moment(dateStr, format, true);
            if (date.isValid()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Kiểm tra xem một ngày giờ có hợp lệ không
     * @param {string} dateTimeStr - Chuỗi ngày giờ cần kiểm tra
     * @returns {boolean} true nếu hợp lệ, false nếu không
     */
    function isValidDateTime(dateTimeStr) {
        if (!dateTimeStr) return false;
        
        // Hỗ trợ nhiều định dạng ngày giờ
        const formats = [
            'DD/MM/YYYY HH:mm:ss',
            'YYYY-MM-DD HH:mm:ss',
            'YYYY/MM/DD HH:mm:ss',
            'DD-MM-YYYY HH:mm:ss',
            'DD/MM/YYYY HH:mm',
            'YYYY-MM-DD HH:mm',
            'YYYY/MM/DD HH:mm',
            'DD-MM-YYYY HH:mm'
        ];
        
        for (const format of formats) {
            const date = moment(dateTimeStr, format, true);
            if (date.isValid()) {
                return true;
            }
        }
        
        return false;
    }
    
    // API công khai
    return {
        loadConfig: loadValidationConfig,
        validateField: validateField,
        validateRecord: validateRecord,
        validateAllData: validateAllData,
        getValidationResults: function() {
            return validationResults;
        }
    };
})();