{% extends 'layouts/base.html' %}
{% load static %}
{% block title %}C<PERSON><PERSON> hình kiểm tra hồ sơ XML{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'AdminLTE-3.0.1/plugins/sweetalert2-theme-bootstrap-4/bootstrap-4.min.css' %}">
<link href="{% static "tabulator/dist/css/tabulator.min.css" %}" rel="stylesheet">
<link href="{% static "tabulator/dist/css/tabulator_bootstrap4.min.css" %}" rel="stylesheet">
<style>
    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 .25rem .25rem;
    }
    .validation-tree {
        max-height: 600px;
        overflow-y: auto;
    }
    .field-config-container {
        max-height: 600px;
        overflow-y: auto;
    }
    .validation-rule {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
    }
    .validation-rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    .validation-rule-body {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    .validation-rule-item {
        flex: 1;
        min-width: 200px;
    }
    .rule-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 10px;
    }
    .tabulator-tree-level-0 {
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Cấu hình kiểm tra hồ sơ XML</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'xml4750:list_xml' %}">Quản lý XML</a></li>
                        <li class="breadcrumb-item active">Cấu hình kiểm tra</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" id="validationTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="basic-tab" data-toggle="tab" href="#basic" role="tab" aria-controls="basic" aria-selected="true">Cấu hình cơ bản</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="advanced-tab" data-toggle="tab" href="#advanced" role="tab" aria-controls="advanced" aria-selected="false">Cấu hình nâng cao</a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="tab-content" id="validationTabsContent">
                        <!-- Tab 1: Cấu hình cơ bản -->
                        <div class="tab-pane fade show active" id="basic" role="tabpanel" aria-labelledby="basic-tab">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Cấu hình trường dữ liệu XML</h3>
                                    <div class="card-tools">
                                        <button type="button" id="load-all-fields" class="btn btn-info btn-sm mr-2">
                                            <i class="fas fa-sync"></i> Tải tất cả trường
                                        </button>
                                        <button type="button" id="save-basic-config" class="btn btn-primary btn-sm">
                                            <i class="fas fa-save"></i> Lưu cấu hình
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div id="field-config-table" class="field-config-container"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Tab 2: Cấu hình nâng cao -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel" aria-labelledby="advanced-tab">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <button type="button" id="add-validation-rule" class="btn btn-success">
                                        <i class="fas fa-plus"></i> Thêm quy tắc kiểm tra
                                    </button>
                                    <button type="button" id="save-advanced-config" class="btn btn-primary ml-2">
                                        <i class="fas fa-save"></i> Lưu cấu hình
                                    </button>
                                </div>
                            </div>
                            
                            <div id="validation-rules-container">
                                <!-- Các quy tắc kiểm tra sẽ được thêm vào đây -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Template cho quy tắc kiểm tra mới -->
<template id="validation-rule-template">
    <div class="validation-rule" data-rule-id="{rule-id}">
        <div class="validation-rule-header">
            <h5>Quy tắc kiểm tra #<span class="rule-number">{rule-number}</span></h5>
            <div>
                <button type="button" class="btn btn-danger btn-sm delete-rule">
                    <i class="fas fa-trash"></i> Xóa
                </button>
            </div>
        </div>
        <div class="validation-rule-body">
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>XML nguồn</label>
                    <select class="form-control source-xml-type">
                        <option value="">-- Chọn loại XML --</option>
                        <option value="XML1">XML1</option>
                        <option value="XML2">XML2</option>
                        <option value="XML3">XML3</option>
                        <option value="XML4">XML4</option>
                        <option value="XML5">XML5</option>
                        <option value="XML6">XML6</option>
                        <option value="XML7">XML7</option>
                        <option value="XML8">XML8</option>
                        <option value="XML9">XML9</option>
                        <option value="XML10">XML10</option>
                        <option value="XML11">XML11</option>
                        <option value="XML12">XML12</option>
                        <option value="XML13">XML13</option>
                        <option value="XML14">XML14</option>
                        <option value="XML15">XML15</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trường nguồn</label>
                    <select class="form-control source-field">
                        <option value="">-- Chọn trường --</option>
                    </select>
                </div>
            </div>
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>Điều kiện</label>
                    <select class="form-control condition">
                        <option value="equal">Bằng (=)</option>
                        <option value="not_equal">Không bằng (!=)</option>
                        <option value="greater">Lớn hơn (>)</option>
                        <option value="greater_equal">Lớn hơn hoặc bằng (>=)</option>
                        <option value="less">Nhỏ hơn (<)</option>
                        <option value="less_equal">Nhỏ hơn hoặc bằng (<=)</option>
                        <option value="contains">Chứa</option>
                        <option value="not_contains">Không chứa</option>
                        <option value="starts_with">Bắt đầu bằng</option>
                        <option value="ends_with">Kết thúc bằng</option>
                        <option value="regex">Khớp biểu thức chính quy</option>
                        <option value="custom">Tùy chỉnh (JavaScript)</option>
                    </select>
                </div>
                <div class="form-group custom-condition-container" style="display: none;">
                    <label>Biểu thức JavaScript</label>
                    <textarea class="form-control custom-condition" rows="3" placeholder="return sourceValue > targetValue;"></textarea>
                    <small class="form-text text-muted">Sử dụng biến sourceValue và targetValue</small>
                </div>
            </div>
            <div class="validation-rule-item">
                <div class="form-group">
                    <label>XML đích</label>
                    <select class="form-control target-xml-type">
                        <option value="">-- Chọn loại XML --</option>
                        <option value="XML1">XML1</option>
                        <option value="XML2">XML2</option>
                        <option value="XML3">XML3</option>
                        <option value="XML4">XML4</option>
                        <option value="XML5">XML5</option>
                        <option value="XML6">XML6</option>
                        <option value="XML7">XML7</option>
                        <option value="XML8">XML8</option>
                        <option value="XML9">XML9</option>
                        <option value="XML10">XML10</option>
                        <option value="XML11">XML11</option>
                        <option value="XML12">XML12</option>
                        <option value="XML13">XML13</option>
                        <option value="XML14">XML14</option>
                        <option value="XML15">XML15</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Trường đích</label>
                    <select class="form-control target-field">
                        <option value="">-- Chọn trường --</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label>Thông báo lỗi</label>
            <input type="text" class="form-control error-message" placeholder="Nhập thông báo lỗi hiển thị khi quy tắc không thỏa mãn">
        </div>
        <div class="form-group">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input is-blocking" id="blocking-{rule-id}">
                <label class="custom-control-label" for="blocking-{rule-id}">Chặn lưu dữ liệu khi không thỏa mãn</label>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script src="{% static "tabulator/dist/js/tabulator.min.js" %}"></script>
<script>
    $(function() {
        // Dữ liệu cho Tabulator với grouping
        let allFieldsData = [];

        // Hàm tải tất cả trường từ tất cả XML types
        function loadAllXmlFields() {
            const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
            const xmlDescriptions = {
                'XML0': 'Thông tin checkin',
                'XML1': 'Thông tin tổng hợp',
                'XML2': 'Chi phí khám chữa bệnh',
                'XML3': 'Thông tin thuốc',
                'XML4': 'Dịch vụ cận lâm sàng',
                'XML5': 'Diễn biến bệnh',
                'XML6': 'Hồ sơ HIV/AIDS',
                'XML7': 'Giấy ra viện',
                'XML8': 'Tóm tắt hồ sơ bệnh án',
                'XML9': 'Giấy chuyển viện',
                'XML10': 'Giấy nghỉ dưỡng thai',
                'XML11': 'Giấy nghỉ việc BHXH',
                'XML12': 'Giám định y khoa',
                'XML13': 'Thông tin thuốc kháng sinh',
                'XML14': 'Thông tin phẫu thuật',
                'XML15': 'Quản lý điều trị lao'
            };

            allFieldsData = [];

            // Tải từng XML type
            const promises = xmlTypes.map(xmlType => {
                return new Promise((resolve) => {
                    $.ajax({
                        url: "{% url 'xml4750:get_xml_fields' %}",
                        type: "GET",
                        data: { xml_type: xmlType },
                        success: function(response) {
                            if (response.success && response.fields.length > 0) {
                                response.fields.forEach(field => {
                                    allFieldsData.push({
                                        xmlType: xmlType,
                                        xmlDescription: xmlDescriptions[xmlType],
                                        xmlGroup: `${xmlType} - ${xmlDescriptions[xmlType]}`,
                                        field: field.name,
                                        description: field.description,
                                        dataType: getDefaultDataType(field.name),
                                        required: isRequiredField(xmlType, field.name),
                                        format: "",
                                        defaultValue: "",
                                        warning: false,
                                        blocking: false
                                    });
                                });
                            }
                            resolve();
                        },
                        error: function() {
                            console.warn(`Could not load fields for ${xmlType}`);
                            resolve();
                        }
                    });
                });
            });

            return Promise.all(promises);
        }

        // Hàm xác định kiểu dữ liệu mặc định dựa trên tên trường
        function getDefaultDataType(fieldName) {
            const lowerField = fieldName.toLowerCase();

            if (lowerField.includes('ngay') || lowerField.includes('date')) {
                return 'date';
            }
            if (lowerField.includes('gio') || lowerField.includes('time')) {
                return 'datetime';
            }
            if (lowerField.includes('gia') || lowerField.includes('tien') ||
                lowerField.includes('luong') || lowerField.includes('so')) {
                return 'number';
            }
            if (lowerField.includes('check') || lowerField.includes('flag')) {
                return 'boolean';
            }

            return 'string';
        }

        // Hàm xác định trường bắt buộc dựa trên tên trường và XML type
        function isRequiredField(xmlType, fieldName) {
            const requiredFields = {
                'XML0': ['maLK', 'maCSKCB'],
                'XML1': ['maLK', 'maBN', 'hoTen', 'ngaySinh', 'gioiTinh'],
                'XML2': ['maLK', 'maDV', 'soLuong', 'donGia'],
                'XML3': ['maLK', 'maThuoc', 'soLuong', 'donGia']
            };

            const required = requiredFields[xmlType] || [];
            return required.includes(fieldName);
        }

        // Khởi tạo Tabulator cho cấu hình trường với grouping
        let fieldConfigTable = new Tabulator("#field-config-table", {
            layout: "fitColumns",
            height: 600,
            placeholder: "Nhấn 'Tải tất cả trường' để hiển thị cấu hình",
            groupBy: "xmlGroup",
            groupStartOpen: false,
            groupHeader: function(value, count, data, group) {
                return `<i class="fas fa-file-code"></i> ${value} <span class="badge badge-secondary ml-2">${count} trường</span>`;
            },
            columns: [
                {title: "Trường", field: "field", width: 150, headerFilter: true},
                {title: "Mô tả", field: "description", width: 200},
                {title: "Kiểu dữ liệu", field: "dataType", width: 120, editor: "select", editorParams: {
                    values: ["string", "number", "date", "datetime", "boolean"]
                }},
                {title: "Bắt buộc", field: "required", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"},
                {title: "Định dạng", field: "format", width: 120, editor: "input", placeholder: "VD: YYYYMMDD"},
                {title: "Giá trị mặc định", field: "defaultValue", width: 120, editor: "input"},
                {title: "Cảnh báo", field: "warning", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"},
                {title: "Chặn lưu", field: "blocking", width: 80, editor: "tickCross", formatter: "tickCross", hozAlign: "center"}
            ],
            cellEdited: function(cell) {
                // Đánh dấu dữ liệu đã thay đổi
                const row = cell.getRow();
                row.getElement().style.backgroundColor = "#fff3cd";
            }
        });

        // Xử lý sự kiện tải tất cả trường
        $('#load-all-fields').click(function() {
            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang tải...');

            loadAllXmlFields().then(() => {
                // Tải cấu hình hiện có từ server
                loadExistingConfigs().then(() => {
                    // Cập nhật dữ liệu vào bảng
                    fieldConfigTable.setData(allFieldsData);
                    btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Tải tất cả trường');

                    Swal.fire('Thành công', `Đã tải ${allFieldsData.length} trường từ ${new Set(allFieldsData.map(f => f.xmlType)).size} loại XML`, 'success');
                });
            }).catch(error => {
                console.error('Error loading fields:', error);
                btn.prop('disabled', false).html('<i class="fas fa-sync"></i> Tải tất cả trường');
                Swal.fire('Lỗi', 'Không thể tải dữ liệu trường', 'error');
            });
        });

        // Hàm tải cấu hình hiện có từ server
        function loadExistingConfigs() {
            return new Promise((resolve) => {
                $.ajax({
                    url: "{% url 'xml4750:get_all_field_configs' %}",
                    type: "GET",
                    success: function(response) {
                        if (response.success) {
                            // Cập nhật cấu hình hiện có vào dữ liệu
                            allFieldsData.forEach(field => {
                                const configKey = `${field.xmlType}.${field.field}`;
                                const config = response.configs[configKey];
                                if (config) {
                                    field.dataType = config.dataType || field.dataType;
                                    field.required = config.required || field.required;
                                    field.format = config.format || field.format;
                                    field.blocking = config.blocking !== undefined ? config.blocking : field.blocking;
                                }
                            });
                        }
                        resolve();
                    },
                    error: function() {
                        console.warn('Could not load existing configs');
                        resolve();
                    }
                });
            });
        }

        // Hàm tải cấu hình cho một trường cụ thể
        function loadFieldConfig(xmlType, fieldName) {
            // Trong thực tế, bạn sẽ tải dữ liệu từ server
            // Ở đây chúng ta sẽ mô phỏng bằng cách tạo dữ liệu mẫu
            $.ajax({
                url: "{% url 'xml4750:get_field_config' %}",
                type: "GET",
                data: {
                    xml_type: xmlType,
                    field_name: fieldName
                },
                success: function(response) {
                    if (response.success) {
                        fieldConfigTable.setData([response.config]);
                    } else {
                        // Nếu chưa có cấu hình, tạo một cấu hình mặc định
                        const defaultConfig = {
                            field: fieldName,
                            xmlType: xmlType,
                            dataType: getDefaultDataType(fieldName),
                            required: isRequiredField(xmlType, fieldName),
                            format: "",
                            defaultValue: "",
                            description: "",
                            warning: false,
                            blocking: false
                        };
                        fieldConfigTable.setData([defaultConfig]);
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading field config:", error);
                    Swal.fire('Lỗi', 'Không thể tải cấu hình trường', 'error');
                }
            });
        }

        // Hàm tải cấu hình cho một loại XML
        function loadXmlTypeConfig(xmlType) {
            $.ajax({
                url: "{% url 'xml4750:get_xml_type_config' %}",
                type: "GET",
                data: {
                    xml_type: xmlType
                },
                success: function(response) {
                    if (response.success) {
                        fieldConfigTable.setData(response.configs);
                    } else {
                        // Nếu chưa có cấu hình, hiển thị thông báo
                        fieldConfigTable.setData([]);
                        Swal.fire('Thông báo', 'Chưa có cấu hình cho loại XML này', 'info');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading XML type config:", error);
                    Swal.fire('Lỗi', 'Không thể tải cấu hình loại XML', 'error');
                }
            });
        }

        // Hàm xác định kiểu dữ liệu mặc định dựa trên tên trường
        function getDefaultDataType(fieldName) {
            if (fieldName.includes('ngay') || fieldName.includes('Ngay')) {
                return "date";
            } else if (fieldName.includes('so') || fieldName.includes('So') || 
                       fieldName.includes('tien') || fieldName.includes('Tien') ||
                       fieldName.includes('gia') || fieldName.includes('Gia')) {
                return "number";
            } else {
                return "string";
            }
        }

        // Hàm xác định trường bắt buộc
        function isRequiredField(xmlType, fieldName) {
            // Danh sách các trường bắt buộc theo loại XML
            const requiredFields = {
                'XML0': ['maLK', 'maCSKCB'],
                'XML1': ['maLK', 'hoTen', 'ngaySinh', 'maTheBHYT'],
                'XML2': ['maLK', 'maDV', 'soLuong', 'donGia'],
                'XML3': ['maLK', 'maThuoc', 'soLuong', 'donGia']
                // Thêm các loại XML khác
            };
            
            return requiredFields[xmlType] && requiredFields[xmlType].includes(fieldName);
        }

        // Lưu cấu hình cơ bản
        $('#save-basic-config').click(function() {
            const configData = fieldConfigTable.getData();

            if (configData.length === 0) {
                Swal.fire('Cảnh báo', 'Không có dữ liệu cấu hình để lưu', 'warning');
                return;
            }

            const btn = $(this);
            btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Đang lưu...');

            $.ajax({
                url: "{% url 'xml4750:save_field_config' %}",
                type: "POST",
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'configs': JSON.stringify(configData)
                },
                success: function(response) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');

                    if (response.success) {
                        // Reset background color của các dòng đã chỉnh sửa
                        fieldConfigTable.getRows().forEach(row => {
                            row.getElement().style.backgroundColor = "";
                        });

                        Swal.fire('Thành công', `Đã lưu cấu hình cho ${configData.length} trường`, 'success');
                    } else {
                        Swal.fire('Lỗi', response.message || 'Không thể lưu cấu hình', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    btn.prop('disabled', false).html('<i class="fas fa-save"></i> Lưu cấu hình');
                    console.error("Error saving config:", error);
                    Swal.fire('Lỗi', 'Không thể lưu cấu hình', 'error');
                }
            });
        });

        // Biến đếm số quy tắc
        let ruleCounter = 0;

        // Thêm quy tắc kiểm tra mới
        $('#add-validation-rule').click(function() {
            ruleCounter++;
            const ruleId = 'rule-' + Date.now();
            let template = $('#validation-rule-template').html();

            if (!template) {
                console.error('Template not found');
                return;
            }

            template = template.replace(/{rule-id}/g, ruleId)
                              .replace(/{rule-number}/g, ruleCounter);

            $('#validation-rules-container').append(template);

            // Cập nhật các dropdown trường khi chọn loại XML
            setupXmlFieldSelectors(ruleId);

            // Xử lý hiển thị/ẩn điều kiện tùy chỉnh
            setupCustomCondition(ruleId);

            console.log('Added validation rule:', ruleId);
        });

        // Xóa quy tắc kiểm tra
        $(document).on('click', '.delete-rule', function() {
            const rule = $(this).closest('.validation-rule');
            
            Swal.fire({
                title: 'Xác nhận xóa',
                text: 'Bạn có chắc chắn muốn xóa quy tắc kiểm tra này?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Xóa',
                cancelButtonText: 'Hủy'
            }).then((result) => {
                if (result.isConfirmed) {
                    rule.remove();
                    updateRuleNumbers();
                }
            });
        });

        // Cập nhật số thứ tự của các quy tắc
        function updateRuleNumbers() {
            $('.validation-rule').each(function(index) {
                $(this).find('.rule-number').text(index + 1);
            });
        }

        // Thiết lập các dropdown trường khi chọn loại XML
        function setupXmlFieldSelectors(ruleId) {
            const rule = $(`.validation-rule[data-rule-id="${ruleId}"]`);

            if (rule.length === 0) {
                console.error('Rule element not found:', ruleId);
                return;
            }

            // Source XML Type change
            rule.find('.source-xml-type').off('change').on('change', function() {
                const xmlType = $(this).val();
                const sourceFieldSelect = rule.find('.source-field');

                if (xmlType) {
                    loadFieldsForXmlType(xmlType, sourceFieldSelect);
                } else {
                    sourceFieldSelect.html('<option value="">-- Chọn trường --</option>');
                }
            });

            // Target XML Type change
            rule.find('.target-xml-type').off('change').on('change', function() {
                const xmlType = $(this).val();
                const targetFieldSelect = rule.find('.target-field');

                if (xmlType) {
                    loadFieldsForXmlType(xmlType, targetFieldSelect);
                } else {
                    targetFieldSelect.html('<option value="">-- Chọn trường --</option>');
                }
            });
        }

        // Thiết lập hiển thị/ẩn điều kiện tùy chỉnh
        function setupCustomCondition(ruleId) {
            const rule = $(`.validation-rule[data-rule-id="${ruleId}"]`);

            if (rule.length === 0) {
                console.error('Rule element not found for custom condition:', ruleId);
                return;
            }

            rule.find('.condition').off('change').on('change', function() {
                const condition = $(this).val();
                const customContainer = rule.find('.custom-condition-container');

                if (condition === 'custom') {
                    customContainer.show();
                } else {
                    customContainer.hide();
                }
            });
        }

        // Tải danh sách trường cho loại XML
        function loadFieldsForXmlType(xmlType, selectElement, callback) {
            if (!xmlType || !selectElement || selectElement.length === 0) {
                console.error('Invalid parameters for loadFieldsForXmlType');
                return;
            }

            // Hiển thị loading
            selectElement.html('<option value="">-- Đang tải... --</option>');

            $.ajax({
                url: "{% url 'xml4750:get_xml_fields' %}",
                type: "GET",
                data: {
                    xml_type: xmlType
                },
                success: function(response) {
                    if (response.success && response.fields) {
                        let options = '<option value="">-- Chọn trường --</option>';
                        response.fields.forEach(field => {
                            const description = field.description || field.name;
                            options += `<option value="${field.name}">${field.name} (${description})</option>`;
                        });
                        selectElement.html(options);

                        if (typeof callback === 'function') {
                            callback();
                        }
                    } else {
                        selectElement.html('<option value="">-- Không có dữ liệu --</option>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error loading fields for", xmlType, ":", error);
                    selectElement.html('<option value="">-- Lỗi tải dữ liệu --</option>');
                }
            });
        }

        // Lưu cấu hình nâng cao
        $('#save-advanced-config').click(function() {
            const rules = [];

            $('.validation-rule').each(function() {
                const rule = $(this);
                const ruleId = rule.data('rule-id');

                const sourceXmlType = rule.find('.source-xml-type').val();
                const sourceField = rule.find('.source-field').val();
                const condition = rule.find('.condition').val();
                const targetXmlType = rule.find('.target-xml-type').val();
                const targetField = rule.find('.target-field').val();
                const errorMessage = rule.find('.error-message').val();
                const isBlocking = rule.find('.is-blocking').prop('checked');

                // Kiểm tra dữ liệu hợp lệ
                if (!sourceXmlType || !sourceField || !condition || !targetXmlType || !targetField) {
                    Swal.fire('Cảnh báo', 'Vui lòng điền đầy đủ thông tin cho tất cả các quy tắc', 'warning');
                    return false;
                }

                // Thêm custom condition nếu có
                let customCondition = null;
                if (condition === 'custom') {
                    customCondition = rule.find('.custom-condition').val();
                    if (!customCondition) {
                        Swal.fire('Cảnh báo', 'Vui lòng nhập biểu thức JavaScript cho điều kiện tùy chỉnh', 'warning');
                        return false;
                    }
                }

                rules.push({
                    id: ruleId,
                    sourceXmlType: sourceXmlType,
                    sourceField: sourceField,
                    condition: condition,
                    targetXmlType: targetXmlType,
                    targetField: targetField,
                    errorMessage: errorMessage || `${sourceXmlType}.${sourceField} phải ${getConditionText(condition)} ${targetXmlType}.${targetField}`,
                    isBlocking: isBlocking,
                    customCondition: customCondition
                });
            });

            if (rules.length === 0) {
                Swal.fire('Cảnh báo', 'Không có quy tắc nào để lưu', 'warning');
                return;
            }

            $.ajax({
                url: "{% url 'xml4750:save_validation_rules' %}",
                type: "POST",
                data: {
                    'csrfmiddlewaretoken': '{{ csrf_token }}',
                    'rules': JSON.stringify(rules)
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('Thành công', 'Đã lưu quy tắc kiểm tra thành công', 'success');
                    } else {
                        Swal.fire('Lỗi', response.message || 'Không thể lưu quy tắc kiểm tra', 'error');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error saving rules:", error);
                    Swal.fire('Lỗi', 'Không thể lưu quy tắc kiểm tra', 'error');
                }
            });
        });
            
            // Hàm lấy text mô tả điều kiện
            function getConditionText(condition) {
                const conditionTexts = {
                    'equal': 'bằng',
                    'not_equal': 'không bằng',
                    'greater': 'lớn hơn',
                    'greater_equal': 'lớn hơn hoặc bằng',
                    'less': 'nhỏ hơn',
                    'less_equal': 'nhỏ hơn hoặc bằng',
                    'contains': 'chứa',
                    'not_contains': 'không chứa',
                    'starts_with': 'bắt đầu bằng',
                    'ends_with': 'kết thúc bằng',
                    'regex': 'khớp với',
                    'custom': 'thỏa mãn điều kiện với'
                };
                
                return conditionTexts[condition] || condition;
            }
            
            // Tải các quy tắc đã lưu khi trang được tải
            function loadSavedRules() {
                $.ajax({
                    url: "{% url 'xml4750:get_validation_rules' %}",
                    type: "GET",
                    success: function(response) {
                        if (response.success && response.rules && response.rules.length > 0) {
                            // Xóa tất cả các quy tắc hiện tại
                            $('#validation-rules-container').empty();
                            ruleCounter = 0;

                            // Thêm các quy tắc đã lưu
                            response.rules.forEach((rule, index) => {
                                ruleCounter++;
                                const ruleId = rule.id || 'rule-' + Date.now() + '-' + ruleCounter;
                                let template = $('#validation-rule-template').html();

                                if (!template) {
                                    console.error('Template not found when loading saved rules');
                                    return;
                                }

                                template = template.replace(/{rule-id}/g, ruleId)
                                                  .replace(/{rule-number}/g, ruleCounter);

                                $('#validation-rules-container').append(template);

                                const ruleElement = $(`.validation-rule[data-rule-id="${ruleId}"]`);

                                if (ruleElement.length === 0) {
                                    console.error('Rule element not created:', ruleId);
                                    return;
                                }

                                // Thiết lập các giá trị
                                ruleElement.find('.source-xml-type').val(rule.sourceXmlType);
                                ruleElement.find('.condition').val(rule.condition);
                                ruleElement.find('.target-xml-type').val(rule.targetXmlType);
                                ruleElement.find('.error-message').val(rule.errorMessage);
                                ruleElement.find('.is-blocking').prop('checked', rule.isBlocking);

                                // Hiển thị custom condition nếu cần
                                if (rule.condition === 'custom') {
                                    ruleElement.find('.custom-condition-container').show();
                                    ruleElement.find('.custom-condition').val(rule.customCondition);
                                }

                                // Tải các trường cho source XML
                                if (rule.sourceXmlType) {
                                    loadFieldsForXmlType(rule.sourceXmlType, ruleElement.find('.source-field'), function() {
                                        ruleElement.find('.source-field').val(rule.sourceField);
                                    });
                                }

                                // Tải các trường cho target XML
                                if (rule.targetXmlType) {
                                    loadFieldsForXmlType(rule.targetXmlType, ruleElement.find('.target-field'), function() {
                                        ruleElement.find('.target-field').val(rule.targetField);
                                    });
                                }

                                // Thiết lập các event handlers
                                setupXmlFieldSelectors(ruleId);
                                setupCustomCondition(ruleId);
                            });

                            console.log(`Loaded ${response.rules.length} validation rules`);
                        } else {
                            console.log('No saved rules found, adding default rule');
                            // Thêm một quy tắc trống mặc định
                            setTimeout(() => {
                                $('#add-validation-rule').trigger('click');
                            }, 100);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error loading saved rules:", error);
                        // Thêm một quy tắc trống nếu có lỗi
                        setTimeout(() => {
                            $('#add-validation-rule').trigger('click');
                        }, 100);
                    }
                });
            }
            
            // Phiên bản mở rộng của hàm loadFieldsForXmlType với callback
            function loadFieldsForXmlType(xmlType, selectElement, callback) {
                $.ajax({
                    url: "{% url 'xml4750:get_xml_fields' %}",
                    type: "GET",
                    data: {
                        xml_type: xmlType
                    },
                    success: function(response) {
                        if (response.success) {
                            let options = '<option value="">-- Chọn trường --</option>';
                            response.fields.forEach(field => {
                                options += `<option value="${field.name}">${field.name} (${field.description || field.name})</option>`;
                            });
                            selectElement.html(options);
                            
                            if (typeof callback === 'function') {
                                callback();
                            }
                        } else {
                            selectElement.html('<option value="">-- Không có dữ liệu --</option>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error loading fields:", error);
                        selectElement.html('<option value="">-- Lỗi tải dữ liệu --</option>');
                    }
                });
            }
            
            // Khởi tạo khi trang được tải
            loadSavedRules();

            // Tự động tải tất cả trường khi vào trang
            setTimeout(() => {
                $('#load-all-fields').trigger('click');
            }, 500);
        });
    </script>
    
    <!-- Thêm script cho validation -->
    <script src="{% static 'js/xml4750/validation_config.js' %}"></script>
    <script src="{% static 'js/xml4750/validation.js' %}"></script>
{% endblock %}