import decimal
import json
import re
import zipfile
from io import BytesIO
import xml.etree.ElementTree as ET # Ensure ET is imported if not already
from datetime import datetime # Ensure datetime is imported
import openpyxl
from openpyxl.styles import <PERSON>ont, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import pandas as pd
from .utils import export_model_to_xml, encode_xml_to_base64 # Assuming these are in utils
# import datetime # Thay thế bằng import tường minh hơn bên d<PERSON><PERSON><PERSON>
from datetime import date as std_date
from datetime import datetime as std_datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Q
from .forms import XML_FORM_MAP
from django.core.exceptions import FieldDoesNotExist
from django.db import transaction, IntegrityError, DataError
from django.core.paginator import Paginator
from .utils import (
    parse_xml_to_model, XML_MODEL_MAP, parse_multiple_xml_to_models,
    extract_xml_sections_from_file_content
)

from .models import ( models,
    XML0Model, XML1Model, XML2Model, XML3Model, XML4Model, XML5Model,
    XML6Model, XML7Model, XML8Model, XML9Model, XML10Model, XML11Model,
    XML12Model, XML13Model, XML14Model, XML15Model,
    XML0FieldConfig, XML1FieldConfig, XML2FieldConfig, XML3FieldConfig, XML4FieldConfig, XML5FieldConfig,
    XML6FieldConfig, XML7FieldConfig, XML8FieldConfig, XML9FieldConfig, XML10FieldConfig, XML11FieldConfig,
    XML12FieldConfig, XML13FieldConfig, XML14FieldConfig, XML15FieldConfig, ValidationRule
)
from django.forms.models import model_to_dict

# Dictionary mapping XML types to their FieldConfig models
XML_FIELD_CONFIG_MAP = {
    'XML0': XML0FieldConfig,
    'XML1': XML1FieldConfig,
    'XML2': XML2FieldConfig,
    'XML3': XML3FieldConfig,
    'XML4': XML4FieldConfig,
    'XML5': XML5FieldConfig,
    'XML6': XML6FieldConfig,
    'XML7': XML7FieldConfig,
    'XML8': XML8FieldConfig,
    'XML9': XML9FieldConfig,
    'XML10': XML10FieldConfig,
    'XML11': XML11FieldConfig,
    'XML12': XML12FieldConfig,
    'XML13': XML13FieldConfig,
    'XML14': XML14FieldConfig,
    'XML15': XML15FieldConfig,
}

# Dictionary to map XML type to model
XML_MODEL_MAP = {
    'XML0': XML0Model,
    'XML1': XML1Model,
    'XML2': XML2Model,
    'XML3': XML3Model,
    'XML4': XML4Model,
    'XML5': XML5Model,
    'XML6': XML6Model,
    'XML7': XML7Model,
    'XML8': XML8Model,
    'XML9': XML9Model,
    'XML10': XML10Model,
    'XML11': XML11Model,
    'XML12': XML12Model,
    'XML13': XML13Model,
    'XML14': XML14Model,
    'XML15': XML15Model,
}

@login_required
def index(request):
    """
    Redirect to list view
    """
    # Chuyển hướng đến trang danh sách XML
    return redirect('xml4750:list_xml')

@login_required
def list_xml(request):
    """
    Display list of all XML records
    """
    # Get search parameters
    search_query = request.GET.get('search', '')
    xml_type = request.GET.get('xml_type', '')

    # Initialize data dictionary
    data = {
        'xml0_list': [],
        'xml1_list': [],
        'xml2_list': [],
        'xml3_list': [],
        'xml4_list': [],
        'xml5_list': [],
        'xml6_list': [],
        'xml7_list': [],
        'xml8_list': [],
        'xml9_list': [],
        'xml10_list': [],
        'xml11_list': [],
        'xml12_list': [],
        'xml13_list': [],
        'xml14_list': [],
        'xml15_list': [],
    }

    # Query data based on search parameters
    for xml_key, model in XML_MODEL_MAP.items():
        if not xml_type or xml_type == xml_key:
            # Sắp xếp theo ID để tránh cảnh báo UnorderedObjectListWarning
            queryset = model.objects.all().order_by('id')
            print("DEBUG: queryset.count() =", queryset.count())

            # Apply search filter if provided
            if search_query:
                # Create a Q object for each field that should be searchable
                q_objects = Q()
                for field in model._meta.fields:
                    if field.get_internal_type() in ['CharField', 'TextField']:
                        q_objects |= Q(**{f"{field.name}__icontains": search_query})

                queryset = queryset.filter(q_objects)

            # Phân trang ở phía server
            paginator = Paginator(queryset, 20)  # Tăng số lượng mục trên mỗi trang lên 20
            page_number = request.GET.get(f'page_{xml_key.lower()}', 1)
            page_obj = paginator.get_page(page_number)

            # Add to data dictionary - chuyển page_obj thành list
            data[f'{xml_key.lower()}_list'] = list(page_obj.object_list)

            # Thêm thông tin phân trang để sử dụng trong JavaScript
            data[f'{xml_key.lower()}_pagination'] = {
                'total': paginator.count,
                'per_page': paginator.per_page,
                'current_page': page_obj.number,
                'last_page': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
            }

    # Add search parameters to context
    data['search_query'] = search_query
    data['xml_type'] = xml_type

    return render(request, 'xml4750/list_4750.html', data)


@login_required
def import_xml(request):
    """
    Import XML file
    """
    if request.method == 'POST' and request.FILES.get('xml_file'):
        xml_file_obj = request.FILES['xml_file']
        selected_group = request.POST.get('xml_type') # This is the group like "XML0", "XML1-15", "XML0-15"

        if not selected_group:
            return JsonResponse({'success': False, 'message': "Vui lòng chọn nhóm XML để xem trước."}, status=400)

        try:
            # Kiểm tra định dạng file
            if not xml_file_obj.name.endswith('.xml'):
                return JsonResponse({'success': False, 'message': "Vui lòng tải lên file XML!"}, status=400)

            xml_file_obj.seek(0) # Đảm bảo con trỏ file ở đầu
            raw_file_content_bytes = xml_file_obj.read()

            try:
                raw_file_content = raw_file_content_bytes.decode('utf-8')
            except UnicodeDecodeError:
                return JsonResponse({'success': False, 'message': "File XML không hợp lệ hoặc encoding không đúng (cần UTF-8)!"}, status=400)

            extracted_content_map = extract_xml_sections_from_file_content(raw_file_content)

            if not extracted_content_map:
                return JsonResponse({'success': False, 'message': "Không thể trích xuất dữ liệu XML từ file. File có thể trống hoặc không đúng định dạng."}, status=400)

            xml_types_to_preview = []
            if selected_group == "XML0":
                xml_types_to_preview = ["XML0"]
            elif selected_group == "XML1-15":
                xml_types_to_preview = [f"XML{i}" for i in range(1, 16)]
            elif selected_group == "XML0-15":
                xml_types_to_preview = [f"XML{i}" for i in range(16)]
            else:
                # Defensive check, should be caught by frontend or earlier validation
                if selected_group in XML_MODEL_MAP: 
                    xml_types_to_preview = [selected_group]
                else:
                    return JsonResponse({'success': False, 'message': f"Lựa chọn nhóm XML không hợp lệ: {selected_group}"}, status=400)

            preview_data_map = {}
            processed_any_type = False

            for xml_type_key in xml_types_to_preview:
                if xml_type_key not in extracted_content_map:
                    continue # Skip if this type is not in the extracted content

                xml_content_for_type = extracted_content_map.get(xml_type_key)
                if not xml_content_for_type:
                    continue

                current_preview_list_for_type = []
                model_class_for_type = XML_MODEL_MAP.get(xml_type_key)
                if not model_class_for_type:
                    continue

                try:
                    if xml_type_key in ['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML9', 'XML15']: # Multi-record types
                        temp_instances = parse_multiple_xml_to_models(xml_type_key, xml_content_for_type)
                        if temp_instances:
                            for instance in temp_instances:
                                data_dict = model_to_dict(instance, exclude=['id'])
                                current_preview_list_for_type.append(data_dict)
                    else: # Single-record types
                        temp_instance = parse_xml_to_model(xml_type_key, xml_content_for_type)
                        if temp_instance:
                            data_dict = model_to_dict(temp_instance, exclude=['id'])
                            current_preview_list_for_type.append(data_dict)
                    
                    if current_preview_list_for_type:
                        preview_data_map[xml_type_key] = current_preview_list_for_type
                        processed_any_type = True
                except Exception as e_parse:
                    print(f"Error parsing {xml_type_key} for preview: {str(e_parse)}")
                    # Optionally, collect these errors to inform the user
                    continue
            
            if not processed_any_type:
                 return JsonResponse({'success': False, 'message': f"Không tìm thấy dữ liệu cho nhóm '{selected_group}' trong file hoặc dữ liệu không hợp lệ."}, status=400)

            return JsonResponse({
                'success': True,
                'preview_data_map': preview_data_map,
                'xml_group_previewed': selected_group,
                'message': f"Dữ liệu cho nhóm {selected_group} đã sẵn sàng để xem trước."
            })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f"Lỗi khi chuẩn bị dữ liệu xem trước: {str(e)}"}, status=500)

    # Nếu không phải POST hoặc không có file, trả về lỗi (hoặc trang form nếu là GET)
    return JsonResponse({'success': False, 'message': "Yêu cầu không hợp lệ."}, status=400)

@login_required
def export_xml(request):
    """
    Export XML files: one for XML0 and one GIAMDINHHS for XML1-15, packaged in a ZIP.
    """
    if request.method == 'POST':
        maLK = request.POST.get('maLK')
        # xml_type is no longer taken from POST, it's determined by logic below

        if not maLK:
            messages.error(request, "Mã liên kết (maLK) là bắt buộc.")
            return redirect('xml4750:list_xml')

        try:
            zip_buffer = BytesIO()
            with zipfile.ZipFile(zip_buffer, "a", zipfile.ZIP_DEFLATED, False) as zip_file:
                # 1. Handle XML0
                xml0_content_str = ""
                try:
                    xml0_model = XML_MODEL_MAP.get('XML0')
                    xml0_record = xml0_model.objects.filter(maLK=maLK).first() # Use filter().first() to avoid DoesNotExist
                    if xml0_record:
                        # Assuming export_model_to_xml returns the direct XML string for XML0
                        xml0_content_str = export_model_to_xml('XML0', xml0_record)
                        zip_file.writestr(f"XML0_{maLK}.xml", xml0_content_str)
                    else:
                        messages.info(request, f"Không tìm thấy dữ liệu XML0 cho mã liên kết: {maLK}")
                except Exception as e_xml0:
                    messages.error(request, f"Lỗi khi tạo XML0 cho mã liên kết {maLK}: {str(e_xml0)}")
                    # Continue to create XML1-15 even if XML0 fails

                # 2. Handle XML1-15 as GIAMDINHHS
                giamdinhhs_root = ET.Element('GIAMDINHHS')
                # Add THONGTINDONVI (assuming a default or configurable MA_CSKCB)
                # You might need to fetch this from settings or a user profile
                default_ma_cskcb = "00000" # Placeholder, replace with actual logic if needed
                thongtindonvi = ET.SubElement(giamdinhhs_root, 'THONGTINDONVI')
                macskcb_element = ET.SubElement(thongtindonvi, 'MACSKCB')
                macskcb_element.text = default_ma_cskcb # Or fetch dynamically

                thongtinhoso = ET.SubElement(giamdinhhs_root, 'THONGTINHOSO')
                ngaylap_element = ET.SubElement(thongtinhoso, 'NGAYLAP')
                ngaylap_element.text = datetime.now().strftime('%Y%m%d%H%M')
                
                danhsachhoso_element = ET.SubElement(thongtinhoso, 'DANHSACHHOSO')
                
                file_count_xml1_15 = 0

                for i in range(1, 16): # XML1 to XML15
                    current_xml_type = f"XML{i}"
                    model_class = XML_MODEL_MAP.get(current_xml_type)
                    if not model_class:
                        continue

                    # Fetch all records for this maLK and xml_type
                    # For single-record types (XML1, XML7, etc.), filter().first() is appropriate.
                    # For multi-record types (XML2, XML3, etc.), you'd fetch all and loop if the structure demands multiple FILEHOSO for the same LOAIHOSO (unlikely for 4750)
                    # or if one maLK can have multiple distinct records of the same XML type (e.g. multiple XML2 for one maLK).
                    # The current `export_model_to_xml` seems to expect a single model_instance.
                    # If an XML type (e.g. XML2) can have multiple records for one maLK, `export_model_to_xml` needs adjustment
                    # or you create one FILEHOSO per record.
                    # For simplicity, assuming one record per maLK for XML types 1, 7, 8, 10-14.
                    # And for list types (2-6, 9, 15), we'd typically have a list wrapper inside the NOIDUNGFILE.
                    
                    records_for_type = model_class.objects.filter(maLK=maLK)
                    if not records_for_type.exists():
                        continue

                    # This part needs careful consideration based on how export_model_to_xml handles list types.
                    # If export_model_to_xml for XML2 generates a DSACH_CHI_TIET_THUOC with all items, then one record is fine.
                    record_to_export = records_for_type.first() # Or iterate if multiple distinct records of same type per maLK

                    try:
                        individual_xml_content = export_model_to_xml(current_xml_type, record_to_export)
                        if individual_xml_content:
                            base64_content = encode_xml_to_base64(individual_xml_content)
                            
                            hoso_element = ET.SubElement(danhsachhoso_element, 'HOSO')
                            filehoso_element = ET.SubElement(hoso_element, 'FILEHOSO')
                            loaihoso_element = ET.SubElement(filehoso_element, 'LOAIHOSO')
                            loaihoso_element.text = current_xml_type
                            noidungfile_element = ET.SubElement(filehoso_element, 'NOIDUNGFILE')
                            noidungfile_element.text = base64_content
                            file_count_xml1_15 += 1
                    except Exception as e_ind:
                        messages.warning(request, f"Lỗi khi tạo nội dung cho {current_xml_type} (maLK: {maLK}): {str(e_ind)}")
                        continue
                
                soluonghoso_element = ET.SubElement(thongtinhoso, 'SOLUONGHOSO') # Add SOLUONGHOSO after populating DANHSACHHOSO
                soluonghoso_element.text = str(file_count_xml1_15)


                if file_count_xml1_15 > 0:
                    xml1_15_content_str = ET.tostring(giamdinhhs_root, encoding='utf-8', method='xml').decode('utf-8')
                    # Add XML declaration manually for the GIAMDINHHS file
                    xml1_15_content_str_with_decl = '<?xml version="1.0" encoding="utf-8"?>\n' + xml1_15_content_str
                    zip_file.writestr(f"XML1-15_{maLK}.xml", xml1_15_content_str_with_decl)
                else:
                    messages.info(request, f"Không tìm thấy dữ liệu XML1-15 cho mã liên kết: {maLK}")

            zip_buffer.seek(0)
            response = HttpResponse(zip_buffer, content_type='application_zip')
            response['Content-Disposition'] = f'attachment; filename="Export_{maLK}.zip"'
            return response

        except Exception as e:
            messages.error(request, f"Lỗi nghiêm trọng khi xuất file ZIP: {str(e)}")
            return redirect('xml4750:list_xml')
            
    # Handle GET request for individual file export (from list view action buttons)
    # This part might need to be adjusted or removed if all exports go through the modal + ZIP logic
    elif request.method == 'GET' and request.GET.get('id') and request.GET.get('type'):
        record_id = request.GET.get('id')
        xml_type_get = request.GET.get('type')
        model_get = XML_MODEL_MAP.get(xml_type_get.upper())

        if not model_get:
            messages.error(request, f"Không tìm thấy loại XML: {xml_type_get}")
            return redirect('xml4750:list_xml')
        try:
            record_get = get_object_or_404(model_get, id=record_id)
            # Using export_model_to_xml directly as it handles XML0 and others differently
            xml_content_get = export_model_to_xml(xml_type_get.upper(), record_get)
            
            response_get = HttpResponse(xml_content_get, content_type='application/xml')
            response_get['Content-Disposition'] = f'attachment; filename="{xml_type_get}_{record_get.maLK}.xml"'
            return response_get
        except Exception as e_get:
            messages.error(request, f"Lỗi khi xuất file XML (GET): {str(e_get)}")
            return redirect('xml4750:list_xml')

    return redirect('xml4750:list_xml')

@login_required
def delete_xml(request, xml_type, record_id):
    """
    Delete XML record based on maLK and ngayTao
    - XML0: Delete only XML0 record by ID
    - XML1: Delete all records from XML1-XML15 with matching maLK and ngayTao
    - XML2-XML15: Delete specific record by ID
    """
    if request.method == 'POST':
        # Get model based on XML type
        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            return JsonResponse({'success': False, 'message': f"Không tìm thấy loại XML: {xml_type}"})

        try:
            if xml_type == 'XML0':
                # XML0: Chỉ xóa bản ghi XML0 theo ID
                record = get_object_or_404(model, id=record_id)
                maLK = record.maLK
                ngayTao = record.ngayTao
                record.delete()
                return JsonResponse({
                    'success': True,
                    'message': f'Đã xóa bản ghi XML0 (MaLK: {maLK}, NgayTao: {ngayTao}).'
                })
                
            elif xml_type == 'XML1':
                # XML1: Xóa tất cả dữ liệu từ XML1-XML15 có cùng maLK và ngayTao
                record = get_object_or_404(model, id=record_id)
                maLK = record.maLK
                ngayTao = record.ngayTao
                
                if not maLK:
                    return JsonResponse({'success': False, 'message': 'Không có Mã Liên Kết để xóa.'})
                
                if not ngayTao:
                    return JsonResponse({'success': False, 'message': 'Không có Ngày Tạo để xóa.'})

                # Xóa tất cả dữ liệu có cùng maLK và ngayTao từ XML1-XML15
                deleted_counts = {}
                total_deleted = 0
                
                with transaction.atomic():
                    for xml_key, model_class in XML_MODEL_MAP.items():
                        # Chỉ xóa từ XML1-XML15, bỏ qua XML0
                        if xml_key != 'XML0' and hasattr(model_class, 'maLK') and hasattr(model_class, 'ngayTao'):
                            try:
                                count, _ = model_class.objects.filter(
                                    maLK=maLK, 
                                    ngayTao=ngayTao
                                ).delete()
                                deleted_counts[xml_key] = count
                                total_deleted += count
                                
                                if count > 0:
                                    print(f"Deleted {count} records from {xml_key} with maLK={maLK}, ngayTao={ngayTao}")
                                    
                            except Exception as e:
                                print(f"Error deleting from {xml_key}: {str(e)}")
                                deleted_counts[xml_key] = 0

                return JsonResponse({
                    'success': True,
                    'message': f'Đã xóa hồ sơ {maLK} (NgayTao: {ngayTao}) - Tổng {total_deleted} bản ghi.',
                    'deleted_counts': deleted_counts,
                    'maLK': maLK,
                    'ngayTao': ngayTao
                })
                
            else:
                # XML2-XML15: Xóa chỉ bản ghi cụ thể theo ID
                record = get_object_or_404(model, id=record_id)
                maLK = record.maLK
                ngayTao = record.ngayTao
                record.delete()
                return JsonResponse({
                    'success': True,
                    'message': f'Đã xóa bản ghi từ {xml_type} (MaLK: {maLK}, NgayTao: {ngayTao}).'
                })

        except Exception as e:
            return JsonResponse({'success': False, 'message': f'Lỗi khi xóa: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ'})


@login_required
def delete_selected_by_malk(request):
    """
    Delete multiple records by maLK and ngayTao (for XML0/XML1 bulk delete)
    """
    if request.method == 'POST':
        try:
            maLK_list = json.loads(request.POST.get('maLK_list', '[]'))
            ngayTao_list = json.loads(request.POST.get('ngayTao_list', '[]'))
            xml_type = request.POST.get('xml_type', 'XML1')
            
            if not maLK_list:
                return JsonResponse({'success': False, 'message': 'Không có Mã Liên Kết nào được chọn.'})
            
            # Ensure we have matching ngayTao for each maLK
            if len(ngayTao_list) != len(maLK_list):
                return JsonResponse({'success': False, 'message': 'Số lượng Mã Liên Kết và Ngày Tạo không khớp.'})

            total_deleted = 0
            deleted_summary = {}
            
            with transaction.atomic():
                for i, maLK in enumerate(maLK_list):
                    ngayTao = ngayTao_list[i] if i < len(ngayTao_list) else None
                    
                    if not ngayTao:
                        continue
                    
                    if xml_type == 'XML0':
                        # Chỉ xóa XML0
                        count, _ = XML0Model.objects.filter(maLK=maLK, ngayTao=ngayTao).delete()
                        total_deleted += count
                        if maLK not in deleted_summary:
                            deleted_summary[maLK] = {}
                        deleted_summary[maLK]['XML0'] = count
                        
                    elif xml_type == 'XML1':
                        # Xóa từ XML1-XML15
                        malk_total = 0
                        if maLK not in deleted_summary:
                            deleted_summary[maLK] = {}
                            
                        for xml_key, model_class in XML_MODEL_MAP.items():
                            if xml_key != 'XML0' and hasattr(model_class, 'maLK') and hasattr(model_class, 'ngayTao'):
                                try:
                                    count, _ = model_class.objects.filter(maLK=maLK, ngayTao=ngayTao).delete()
                                    deleted_summary[maLK][xml_key] = count
                                    malk_total += count
                                except Exception as e:
                                    print(f"Error deleting {xml_key} for maLK {maLK}: {str(e)}")
                                    deleted_summary[maLK][xml_key] = 0
                        
                        total_deleted += malk_total

            return JsonResponse({
                'success': True,
                'message': f'Đã xóa {len(maLK_list)} hồ sơ (tổng {total_deleted} bản ghi).',
                'total_deleted': total_deleted,
                'deleted_summary': deleted_summary
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Dữ liệu JSON không hợp lệ.'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'Lỗi khi xóa: {str(e)}'})

    return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ'})

@login_required
def delete_xml_by_malk(request, maLK_input):
    """
    Thực hiện việc xóa dữ liệu XML từ XML0 - XML15 dựa theo mã liên kết
    Được gọi thông qua AJAX từ giao diện
    maLK_input: Có thể là một maLK đơn lẻ hoặc danh sách các mã liên kết phân cách bằng dấu ;
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ.'})

    if not maLK_input or not isinstance(maLK_input, str):
        return JsonResponse({'success': False, 'message': 'Mã Liên Kết không hợp lệ.'})

    # Xử lý cả trường hợp maLK đơn lẻ và danh sách
    if ';' in maLK_input:
        # Danh sách mã liên kết phân cách bằng dấu ;
        maLK_list = [maLK.strip() for maLK in maLK_input.split(';') if maLK.strip()]
    else:
        # Mã liên kết đơn lẻ
        maLK_list = [maLK_input.strip()] if maLK_input.strip() else []

    if not maLK_list:
        return JsonResponse({'success': False, 'message': 'Không có Mã Liên Kết hợp lệ để xóa.'})

    deleted_counts = {}
    try:
        with transaction.atomic(): # Bắt đầu một giao dịch CSDL
            # Xóa các bản ghi từ XML1Model trước
            # Điều này quan trọng nếu có các ràng buộc khóa ngoại hoặc để logic được rõ ràng
            count_xml1, _ = XML1Model.objects.filter(maLK__in=maLK_list).delete()
            deleted_counts['XML1'] = count_xml1

            # Xóa các bản ghi liên quan từ các model XML khác (XML0, XML2-XML15)
            for xml_key, model in XML_MODEL_MAP.items():
                if xml_key != 'XML1': # Bỏ qua XML1 vì đã xóa ở trên
                    # Chỉ xóa nếu model có trường 'maLK'
                    if hasattr(model, 'maLK'):
                        count, _ = model.objects.filter(maLK__in=maLK_list).delete()
                        deleted_counts[xml_key] = count
                    else:
                        # Ghi log hoặc xử lý các model không có trường maLK nếu cần
                        # print(f"Model {xml_key} không có trường maLK, bỏ qua.")
                        pass
        
        total_deleted = sum(deleted_counts.values())
        # Tạo thông điệp chi tiết hơn
        details_message_parts = [f"{key}: {value} bản ghi" for key, value in deleted_counts.items() if value > 0]
        details_message = ", ".join(details_message_parts)
        
        return JsonResponse({
            'success': True, 
            'message': f'Đã xóa thành công tổng cộng {total_deleted} bản ghi. Chi tiết: {details_message if details_message else "Không có bản ghi nào được xóa từ các bảng phụ."}'
        })
    except Exception as e:
        # Ghi log lỗi để debug
        # logger.error(f"Lỗi khi xóa hàng loạt theo Mã Liên Kết: {str(e)}")
        return JsonResponse({'success': False, 'message': f'Lỗi trong quá trình xóa: {str(e)}'})
@login_required
def list_xml_new(request):
    """
    Trang hiển thị nhiều bảng Tabulator với dữ liệu từ database (XML0 đến XML15)
    """
    context = {}
    # Lấy dữ liệu cho tất cả các loại XML
    for xml_key, model in XML_MODEL_MAP.items():
        try:
            # Lấy tối đa 100 bản ghi để test hiển thị
            queryset = model.objects.all().order_by('id')[:100]
            # Chuyển queryset sang list of dicts để dễ dàng truyền sang JSON trong template
            context[f'{xml_key.lower()}_list'] = list(queryset.values())
        except Exception as e:
            # Log lỗi hoặc xử lý nếu có model nào đó gặp vấn đề
            print(f"Error fetching data for {xml_key}: {e}")
            context[f'{xml_key.lower()}_list'] = [] # Trả về list rỗng nếu lỗi

    # Truyền XML_MODEL_MAP vào context để template có thể lặp qua các loại XML
    context['xml_model_map'] = XML_MODEL_MAP

    return render(request, 'xml4750/list_4750_new.html', context)

@login_required
def xml_data_api(request):
    """
    API endpoint để trả về dữ liệu JSON cho Tabulator với hỗ trợ server-side pagination
    """
    # Lấy parameters từ request với validation tốt hơn
    xml_type = request.GET.get('xml_type', 'xml0').upper()

    # Pagination parameters
    try:
        page = max(1, int(request.GET.get('page', '1')))
    except (ValueError, TypeError):
        page = 1

    try:
        size = max(1, min(100, int(request.GET.get('size', '20'))))  # Giới hạn tối đa 100 records/page
    except (ValueError, TypeError):
        size = 20

    # Search parameters
    search = request.GET.get('search', '').strip()

    # Filter parameters
    filter_time_type = request.GET.get('filter_time_type', '').strip()
    filter_from_date = request.GET.get('filter_from_date', '').strip()
    filter_to_date = request.GET.get('filter_to_date', '').strip()
    filter_object_type = request.GET.get('filter_object_type', '').strip()
    filter_kcb_type = request.GET.get('filter_kcb_type', '').strip()

    # Sorting parameters (Tabulator format)
    sort_field = request.GET.get('sort', '')
    sort_dir = request.GET.get('dir', 'asc')  # asc hoặc desc
    
    # Mapping cho loại KCB
    KCB_TYPE_MAPPING = {
        'ngoai_tru': ['01', '07'],                    # ngoại trú
        'dieu_tri_ngoai_tru': ['02', '05', '06', '08'], # điều trị ngoại trú
        'noi_tru': ['03', '04', '09']                 # nội trú
    }

    # Validate XML type
    if xml_type not in XML_MODEL_MAP:
        return JsonResponse({
            'error': f'Invalid xml_type: {xml_type}',
            'valid_types': list(XML_MODEL_MAP.keys())
        }, status=400)

    model = XML_MODEL_MAP[xml_type]

    try:
        # Get model fields for validation
        model_fields = [f.name for f in model._meta.fields]
        has_maLK = 'maLK' in model_fields

        # Base queryset với ordering mặc định
        default_order = 'id'
        if sort_field and sort_field in model_fields:
            # Validate sort direction
            if sort_dir.lower() not in ['asc', 'desc']:
                sort_dir = 'asc'

            # Apply sorting
            order_field = sort_field if sort_dir.lower() == 'asc' else f'-{sort_field}'
            queryset = model.objects.all().order_by(order_field)
        else:
            queryset = model.objects.all().order_by(default_order)

        # Xử lý logic filter khác nhau cho từng loại XML
        if xml_type.upper() == 'XML1':
            # XML1: Áp dụng các filter trực tiếp
            combined_filters = Q()

            # Apply search filter if provided
            if search:
                q_objects = Q()
                for field in model._meta.fields:
                    if field.get_internal_type() in ['CharField', 'TextField']:
                        q_objects |= Q(**{f"{field.name}__icontains": search})
                combined_filters &= q_objects

            # Apply date range filter
            if filter_time_type and (filter_from_date or filter_to_date):
                field_names = [f.name for f in model._meta.fields]
                if filter_time_type in field_names:
                    date_q = Q()
                    if filter_from_date:
                        from_date_formatted = filter_from_date.replace('-', '')
                        date_q &= Q(**{f"{filter_time_type}__gte": from_date_formatted})
                    if filter_to_date:
                        to_date_formatted = filter_to_date.replace('-', '') + "2359"
                        date_q &= Q(**{f"{filter_time_type}__lte": to_date_formatted})
                    combined_filters &= date_q
                else:
                    print(f"Warning: Field '{filter_time_type}' not found in model {xml_type} for date filtering.")

            # Apply object type filter
            if filter_object_type:
                object_q = Q()
                if filter_object_type == 'bhxhvn':
                    object_q &= ~Q(maTinh='97')
                elif filter_object_type == 'bhxhbqp':
                    object_q &= Q(maTinh='97')
                combined_filters &= object_q

            # Apply KCB type filter
            if filter_kcb_type and 'maLoaiKCB' in model_fields:
                if filter_kcb_type in KCB_TYPE_MAPPING:
                    # Sử dụng danh sách mã cho loại KCB
                    kcb_codes = KCB_TYPE_MAPPING[filter_kcb_type]
                    combined_filters &= Q(maLoaiKCB__in=kcb_codes)
                else:
                    # Fallback: sử dụng giá trị trực tiếp nếu không có trong mapping
                    combined_filters &= Q(maLoaiKCB=filter_kcb_type)

            # Apply all combined filters
            if combined_filters:
                queryset = queryset.filter(combined_filters)
        
        elif xml_type.upper() in ['XML0', 'XML12']:
            # XML0, XML12: Áp dụng các filter trực tiếp mà không phụ thuộc vào maLK từ XML1
            # (Logic lọc cho XML0 và XML12 sẽ được xử lý trong khối 'else' cuối cùng nếu không có maLK__in)
            # Tuy nhiên, nếu client gửi maLK__in cho XML0 hoặc XML12 (dù không logic), chúng ta vẫn có thể xử lý
            malk_list_from_request = request.GET.get('maLK__in')
            if malk_list_from_request and has_maLK:
                ma_lk_list = [malk.strip() for malk in malk_list_from_request.split(',') if malk.strip()]
                if ma_lk_list:
                    queryset = queryset.filter(maLK__in=ma_lk_list)

        elif xml_type.upper() in ['XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML13', 'XML14', 'XML15'] and has_maLK:
            # XML2-XML15 (trừ XML12): Lấy mã liên kết từ XML1 đã được lọc - chỉ khi có field maLK
            xml1_model = XML_MODEL_MAP['XML1']
            xml1_queryset = xml1_model.objects.all()
            xml1_fields = [f.name for f in xml1_model._meta.fields]

            # Ưu tiên sử dụng maLK__in từ request nếu có
            malk_list_from_request = request.GET.get('maLK__in')
            if malk_list_from_request:
                ma_lk_list = [malk.strip() for malk in malk_list_from_request.split(',') if malk.strip()]
                print(f"[{xml_type}] Using maLK list from request: {len(ma_lk_list)} items.")
            else:
                # Nếu không có maLK__in từ request, tự lọc XML1
                print(f"[{xml_type}] maLK__in not provided, filtering XML1 based on other params...")
                xml1_combined_filters = Q()

                # Apply search filter to XML1 if provided
                if search: # Sử dụng biến search chung
                    q_objects_xml1 = Q()
                    for field in xml1_model._meta.fields:
                        if field.get_internal_type() in ['CharField', 'TextField']:
                            q_objects_xml1 |= Q(**{f"{field.name}__icontains": search})
                    if q_objects_xml1: xml1_combined_filters &= q_objects_xml1

                # Apply date range filter to XML1
                if filter_time_type and (filter_from_date or filter_to_date):
                    if filter_time_type in xml1_fields:
                        date_q_xml1 = Q()
                        if filter_from_date:
                            from_date_formatted = filter_from_date.replace('-', '')
                            date_q_xml1 &= Q(**{f"{filter_time_type}__gte": from_date_formatted})
                        if filter_to_date:
                            to_date_formatted = filter_to_date.replace('-', '') + "2359" # Bao gồm cả ngày cuối
                            date_q_xml1 &= Q(**{f"{filter_time_type}__lte": to_date_formatted})
                        if date_q_xml1: xml1_combined_filters &= date_q_xml1

                # Apply object type filter to XML1
                if filter_object_type:
                    object_q_xml1 = Q()
                    if filter_object_type == 'bhxhvn':
                        object_q_xml1 &= ~Q(maTinh='97')
                    elif filter_object_type == 'bhxhbqp':
                        object_q_xml1 &= Q(maTinh='97')
                    if object_q_xml1: xml1_combined_filters &= object_q_xml1

                # Apply KCB type filter to XML1
                if filter_kcb_type and 'maLoaiKCB' in xml1_fields:
                    kcb_q_xml1 = Q()
                    if filter_kcb_type in KCB_TYPE_MAPPING:
                        kcb_codes = KCB_TYPE_MAPPING[filter_kcb_type]
                        kcb_q_xml1 &= Q(maLoaiKCB__in=kcb_codes)
                    else: # Fallback nếu không có trong mapping (ví dụ người dùng nhập trực tiếp)
                        kcb_q_xml1 &= Q(maLoaiKCB=filter_kcb_type)
                    if kcb_q_xml1: xml1_combined_filters &= kcb_q_xml1
                
                # Apply filters to XML1 queryset
                if xml1_combined_filters:
                    xml1_queryset = xml1_queryset.filter(xml1_combined_filters)

                # Lấy danh sách mã liên kết từ XML1 đã được lọc
                xml1_data = xml1_queryset.values_list('maLK', flat=True).distinct()
                ma_lk_list = [malk for malk in xml1_data if malk]

            # Filter current XML type by maLK from XML1
            if ma_lk_list:
                queryset = queryset.filter(maLK__in=ma_lk_list)
            else:
                # Nếu không có maLK nào từ XML1, trả về queryset rỗng
                queryset = queryset.none()

            # Thêm search filter (nếu có) cho XML hiện tại (XML2-15)
            # Điều này cho phép tìm kiếm trong XML2-15 sau khi đã lọc theo maLK
            if search:
                q_objects_current_xml = Q()
                for field in model._meta.fields:
                    if field.get_internal_type() in ['CharField', 'TextField']:
                        q_objects_current_xml |= Q(**{f"{field.name}__icontains": search})
                if q_objects_current_xml: queryset = queryset.filter(q_objects_current_xml)

        else:
            # Các XML không có maLK hoặc không thuộc nhóm XML2-15 (ví dụ XML0, XML12 nếu không có maLK__in)
            combined_filters = Q()

            # Apply search filter if provided
            if search:
                q_objects = Q()
                for field in model._meta.fields:
                    if field.get_internal_type() in ['CharField', 'TextField']:
                        q_objects |= Q(**{f"{field.name}__icontains": search})
                combined_filters &= q_objects

            # Apply date range filter
            if filter_time_type and (filter_from_date or filter_to_date):
                if filter_time_type in model_fields:
                    date_q = Q()
                    if filter_from_date:
                        from_date_formatted = filter_from_date.replace('-', '')
                        date_q &= Q(**{f"{filter_time_type}__gte": from_date_formatted})
                    if filter_to_date:
                        to_date_formatted = filter_to_date.replace('-', '') + "2359"
                        date_q &= Q(**{f"{filter_time_type}__lte": to_date_formatted})
                    combined_filters &= date_q
                else:
                    print(f"Warning: Field '{filter_time_type}' not found in model {xml_type} for date filtering.")
            elif xml_type.upper() == 'XML0' and (filter_from_date or filter_to_date):
                # XML0 mặc định filter theo ngayTao nếu không có filter_time_type
                date_q = Q()
                if filter_from_date:
                    from_date_formatted = filter_from_date.replace('-', '')
                    date_q &= Q(ngayTao__gte=from_date_formatted)
                if filter_to_date:
                    to_date_formatted = filter_to_date.replace('-', '') + "2359"
                    date_q &= Q(ngayTao__lte=to_date_formatted)
                combined_filters &= date_q

            # Apply object type filter (chỉ cho các XML có field tương ứng)
            if filter_object_type:
                object_q = Q()
                if filter_object_type == 'bhxhvn':
                    object_q &= ~Q(maTinh='97')
                elif filter_object_type == 'bhxhbqp':
                    object_q &= Q(maTinh='97')
                if object_q.children:  # Chỉ áp dụng nếu có điều kiện
                    combined_filters &= object_q

            # Apply KCB type filter (chỉ cho các XML có field maLoaiKCB)
            if filter_kcb_type and 'maLoaiKCB' in model_fields:
                if filter_kcb_type in KCB_TYPE_MAPPING:
                    kcb_codes = KCB_TYPE_MAPPING[filter_kcb_type]
                    combined_filters &= Q(maLoaiKCB__in=kcb_codes)
                else:
                    combined_filters &= Q(maLoaiKCB=filter_kcb_type)

            # Apply all combined filters
            if combined_filters:
                queryset = queryset.filter(combined_filters)

        # Pagination
        paginator = Paginator(queryset, size)
        page_obj = paginator.get_page(page)
        
        # Helper function to serialize field values properly
        def serialize_field_value(value):
            """Convert Django field values to JSON-serializable format"""
            if value is None:
                return None
            elif isinstance(value, decimal.Decimal):
                return float(value)
            elif isinstance(value, (std_date, std_datetime)):
                return value.isoformat()
            elif isinstance(value, (str, int, float, bool)):
                return str(value)
            else:
                return str(value)
        
        # Serialize data
        data = []
        for obj in page_obj.object_list:
            item = {}
            for field in model._meta.fields:
                field_name = field.name
                field_value = getattr(obj, field_name)
                
                # Serialize the field value properly
                item[field_name] = serialize_field_value(field_value)
            
            data.append(item)

        print(data)

        # Calculate summary data for specific XML types
        summary_data = {}
        if xml_type in ['XML1', 'XML2', 'XML3'] and queryset.exists():
            try:
                if xml_type == 'XML1':
                    # XML1 summary: tổng các trường tài chính
                    from django.db.models import Sum
                    summary_data = queryset.aggregate(
                        total_records=models.Count('id'),
                        total_tTongChiBV=Sum('tTongChiBV'),
                        total_tTongChiBH=Sum('tTongChiBH'),
                        total_tBHTT=Sum('tBHTT'),
                        total_tBNCCT=Sum('tBNCCT'),
                        total_tBNTT=Sum('tBNTT'),
                        total_tThuoc=Sum('tThuoc'),
                        total_tVTYT=Sum('tVTYT')
                    )
                elif xml_type == 'XML2':
                    # XML2 summary: tổng thuốc và máu
                    from django.db.models import Sum, Case, When, DecimalField
                    summary_data = queryset.aggregate(
                        total_records=models.Count('id'),
                        total_thanhTienBV=Sum('thanhTienBV'),
                        total_thanhTienBH=Sum('thanhTienBH'),
                        total_tBHTT=Sum('tBHTT'),
                        total_tBNCCT=Sum('tBNCCT'),
                        total_tBNTT=Sum('tBNTT'),
                        total_thuoc=Sum(
                            Case(
                                When(maNhom='4', then='tBHTT'),
                                default=0,
                                output_field=DecimalField()
                            )
                        ),
                        total_mau=Sum(
                            Case(
                                When(maNhom='7', then='tBHTT'),
                                default=0,
                                output_field=DecimalField()
                            )
                        )
                    )
                elif xml_type == 'XML3':
                    # XML3 summary: tổng DVKT và VTYT
                    from django.db.models import Sum, Case, When, DecimalField
                    summary_data = queryset.aggregate(
                        total_records=models.Count('id'),
                        total_thanhTienBV=Sum('thanhTienBV'),
                        total_thanhTienBH=Sum('thanhTienBH'),
                        total_tBHTT=Sum('tBHTT'),
                        total_tBNCCT=Sum('tBNCCT'),
                        total_tBNTT=Sum('tBNTT'),
                        total_dvkt=Sum(
                            Case(
                                When(maNhom__in=['1', '2', '3'], then='tBHTT'),
                                default=0,
                                output_field=DecimalField()
                            )
                        ),
                        total_vtyt=Sum(
                            Case(
                                When(maNhom__in=['5', '6', '8', '9', '10', '11'], then='tBHTT'),
                                default=0,
                                output_field=DecimalField()
                            )
                        )
                    )

                # Convert Decimal to float for JSON serialization
                for key, value in summary_data.items():
                    if isinstance(value, decimal.Decimal):
                        summary_data[key] = float(value)
                    elif value is None:
                        summary_data[key] = 0

            except Exception as e:
                print(f"Error calculating summary for {xml_type}: {e}")
                summary_data = {'error': str(e)}

        # Enhanced response data với metadata
        response_data = {
            'data': data,
            'pagination': {
                'total': paginator.count,
                'per_page': paginator.per_page,
                'current_page': page_obj.number,
                'last_page': paginator.num_pages,
                'has_next': page_obj.has_next(),
                'has_previous': page_obj.has_previous(),
                'start_index': page_obj.start_index() if page_obj.object_list else 0,
                'end_index': page_obj.end_index() if page_obj.object_list else 0,
                'total_pages': paginator.num_pages,
                'count_on_page': len(data)
            },
            'summary': summary_data,
            'meta': {
                'xml_type': xml_type,
                'filters_applied': {
                    'search': bool(search),
                    'time_filter': bool(filter_time_type and (filter_from_date or filter_to_date)),
                    'object_filter': bool(filter_object_type),
                    'kcb_filter': bool(filter_kcb_type)
                },
                'sort': {
                    'field': sort_field if sort_field in model_fields else default_order,
                    'direction': sort_dir if sort_field in model_fields else 'asc'
                }
            }
        }

        return JsonResponse(response_data, json_dumps_params={'ensure_ascii': False})
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'error': str(e)}, status=500)
    
@login_required
def save_row(request):
    """
    Saves an entire row of data for a specific XML type via AJAX POST.
    Expects xml_type and row_data (JSON string of the row object).
    """
    if request.method == 'POST':
        xml_type = request.POST.get('xml_type')
        row_data_json = request.POST.get('row_data')

        if not all([xml_type, row_data_json]):
            return JsonResponse({'success': False, 'message': 'Thiếu thông tin cần thiết (xml_type hoặc row_data).'}, status=400)

        model = XML_MODEL_MAP.get(xml_type.upper())
        if not model:
            return JsonResponse({'success': False, 'message': f'Không tìm thấy loại XML: {xml_type}'}, status=400)

        try:
            # Parse the JSON string back into a Python dictionary
            row_data = json.loads(row_data_json)
            
            # Get the record ID from the data
            record_id = row_data.get('id')
            if not record_id:
                 return JsonResponse({'success': False, 'message': 'Thiếu ID bản ghi trong dữ liệu dòng.'}, status=400)

            # Get the existing record
            record = get_object_or_404(model, id=record_id)

            # Update fields from row_data (excluding 'id' and potentially other auto-managed fields)
            for field_name, value in row_data.items():
                if field_name not in ['id', 'ngayTao', 'ngayChinhSua', 'trangThaiGuiBHXH'] and hasattr(record, field_name):
                     setattr(record, field_name, value)

            record.save() # Save the updated record
            return JsonResponse({'success': True, 'message': f'Đã lưu bản ghi {xml_type} ID {record_id} thành công.'})

        except json.JSONDecodeError:
             return JsonResponse({'success': False, 'message': 'Dữ liệu dòng không phải là JSON hợp lệ.'}, status=400)
        except model.DoesNotExist:
             return JsonResponse({'success': False, 'message': f'Không tìm thấy bản ghi {xml_type} với ID {record_id}.'}, status=404)
        except Exception as e:
            # Log the error for debugging on the server side
            # import logging
            # logger = logging.getLogger(__name__)
            # logger.error(f"Error saving row for {xml_type} ID {record_id}: {e}", exc_info=True)
            return JsonResponse({'success': False, 'message': f'Lỗi khi lưu bản ghi: {str(e)}'}, status=500)

    return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ.'}, status=405) # Use 405 for Method Not Allowed

@login_required
def save_preview_data(request):
    if request.method == 'POST':
        xml_group_processed = request.POST.get('xml_group_processed')
        data_to_save_map_json = request.POST.get('data_to_save_map')

        if not xml_group_processed or not data_to_save_map_json:
            return JsonResponse({'success': False, 'message': 'Thiếu thông tin nhóm XML hoặc bản đồ dữ liệu.'}, status=400)

        try:
            data_map = json.loads(data_to_save_map_json)
            if not isinstance(data_map, dict):
                return JsonResponse({'success': False, 'message': 'Dữ liệu gửi lên không phải là một bản đồ (dictionary).'}, status=400)

            total_saved_count = 0
            total_updated_count = 0
            all_error_messages = []
            processed_types_summary = []
            
            # Models that have ngayChinhSua field
            MODELS_WITH_NGAY_CHINH_SUA = [XML0Model, XML1Model]
            
            # Helper function to convert empty strings to None for numeric/date fields
            def clean_field_value(model_cls, field_name, value, fixed_timestamp):
                try:
                    field_obj = model_cls._meta.get_field(field_name)
                    
                    # Handle special date fields that need yyyyMMddHHmm format
                    if field_name in ['ngayTao', 'ngayChinhSua']:
                        # ALWAYS use the fixed timestamp for these fields to ensure consistency
                        return fixed_timestamp
                    
                    # Handle numeric fields
                    elif isinstance(field_obj, (models.IntegerField, models.FloatField, models.DecimalField)):
                        if value == '' or value is None:
                            return None
                        # Try to convert to appropriate type
                        if isinstance(field_obj, models.IntegerField):
                            return int(value) if str(value).strip() else None
                        elif isinstance(field_obj, models.FloatField):
                            return float(value) if str(value).strip() else None
                        elif isinstance(field_obj, models.DecimalField):
                            return decimal.Decimal(str(value)) if str(value).strip() else None
                    
                    # Handle regular date/datetime fields (not ngayTao/ngayChinhSua)
                    elif isinstance(field_obj, (models.DateField, models.DateTimeField)):
                        if value == '' or value is None:
                            return None
                        # If it's already a date/datetime object, return as is
                        if isinstance(value, (std_date, std_datetime)):
                            return value
                        # Try to parse string dates
                        if isinstance(value, str):
                            try:
                                if isinstance(field_obj, models.DateTimeField):
                                    return std_datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                                else:
                                    return std_datetime.strptime(value, '%Y-%m-%d').date()
                            except ValueError:
                                return None
                    
                    # Handle boolean fields
                    elif isinstance(field_obj, models.BooleanField):
                        if isinstance(value, bool):
                            return value
                        if isinstance(value, str):
                            return value.lower() in ('true', '1', 'yes', 'on')
                        return bool(value)
                        
                except FieldDoesNotExist:
                    pass
                
                return value

            def prepare_model_data(model_class, item_data, fixed_timestamp):
                """Prepare and clean data for model creation/update"""
                cleaned_data = {}
                
                for field_name, value in item_data.items():
                    try:
                        field_obj = model_class._meta.get_field(field_name)
                        cleaned_value = clean_field_value(model_class, field_name, value, fixed_timestamp)
                        cleaned_data[field_name] = cleaned_value
                    except FieldDoesNotExist:
                        # Skip fields that don't exist in the model
                        continue
                
                # FORCE set ngayTao for all models (all models should have this field)
                try:
                    model_class._meta.get_field('ngayTao')
                    cleaned_data['ngayTao'] = fixed_timestamp  # ALWAYS override with fixed timestamp
                    print(f"    Setting ngayTao for {model_class.__name__}: {fixed_timestamp}")
                except FieldDoesNotExist:
                    print(f"    WARNING: {model_class.__name__} does not have ngayTao field")
                    pass
                    
                # ONLY set ngayChinhSua for XML0Model and XML1Model
                if model_class in MODELS_WITH_NGAY_CHINH_SUA:
                    try:
                        model_class._meta.get_field('ngayChinhSua')
                        cleaned_data['ngayChinhSua'] = fixed_timestamp  # ALWAYS override with fixed timestamp
                        print(f"    Setting ngayChinhSua for {model_class.__name__}: {fixed_timestamp}")
                    except FieldDoesNotExist:
                        print(f"    WARNING: {model_class.__name__} should have ngayChinhSua field but doesn't")
                        pass
                else:
                    print(f"    Skipping ngayChinhSua for {model_class.__name__} (not in models with ngayChinhSua)")
                
                return cleaned_data

            def save_instance_with_fixed_timestamp(model_class, cleaned_data, fixed_timestamp):
                """Save instance while bypassing model's save() method timestamp override"""
                
                # Create instance without saving
                instance = model_class(**cleaned_data)
                
                # For models that override save() method (XML0Model, XML1Model), 
                # we need to use a different approach to preserve our timestamp
                if model_class in MODELS_WITH_NGAY_CHINH_SUA:
                    print(f"    Using direct database insert for {model_class.__name__} to bypass save() override")
                    
                    # Method 1: Use bulk_create to bypass save() method
                    try:
                        # Set the fixed timestamp again just before saving
                        instance.ngayTao = fixed_timestamp
                        instance.ngayChinhSua = fixed_timestamp
                        
                        # Use bulk_create which bypasses save() method
                        model_class.objects.bulk_create([instance])
                        print(f"    BULK_CREATE SUCCESS: {model_class.__name__} with ngayTao: {fixed_timestamp}")
                        return True
                        
                    except Exception as bulk_error:
                        print(f"    BULK_CREATE FAILED: {bulk_error}")
                        
                        # Method 2: Fallback to regular save but update timestamp after
                        try:
                            instance.save()
                            # Immediately update the timestamp using raw SQL to override the save() method
                            from django.db import connection
                            with connection.cursor() as cursor:
                                table_name = model_class._meta.db_table
                                cursor.execute(
                                    f"UPDATE {table_name} SET ngayTao = %s, ngayChinhSua = %s WHERE id = %s",
                                    [fixed_timestamp, fixed_timestamp, instance.pk]
                                )
                            print(f"    SAVE+UPDATE SUCCESS: {model_class.__name__} with ngayTao: {fixed_timestamp}")
                            return True
                        except Exception as save_error:
                            print(f"    SAVE+UPDATE FAILED: {save_error}")
                            raise save_error
                else:
                    # For other models, use regular save
                    instance.save()
                    print(f"    REGULAR SAVE: {model_class.__name__} with ngayTao: {fixed_timestamp}")
                    return True

            # Step 1: Group data by maLK to ensure each record gets consistent timestamp
            records_by_malk = {}
            
            print("=== GROUPING DATA BY MALK ===")
            for xml_type_key, data_list_for_type in data_map.items():
                if not isinstance(data_list_for_type, list):
                    all_error_messages.append(f"Dữ liệu cho {xml_type_key} không phải là danh sách.")
                    continue

                model_class = XML_MODEL_MAP.get(xml_type_key)
                if not model_class:
                    all_error_messages.append(f"Loại XML không hợp lệ trong bản đồ: {xml_type_key}")
                    continue

                print(f"Processing {xml_type_key} ({model_class.__name__}) with {len(data_list_for_type)} items")
                
                # Group items by maLK
                for item_data in data_list_for_type:
                    ma_lk_value = item_data.get('maLK', 'N/A')
                    
                    if ma_lk_value not in records_by_malk:
                        records_by_malk[ma_lk_value] = {}
                        print(f"  Created new record group for maLK: {ma_lk_value}")
                    
                    if xml_type_key not in records_by_malk[ma_lk_value]:
                        records_by_malk[ma_lk_value][xml_type_key] = []
                    
                    records_by_malk[ma_lk_value][xml_type_key].append({
                        'model_class': model_class,
                        'item_data': item_data
                    })
                    print(f"  Added {xml_type_key} item to maLK: {ma_lk_value}")

            print(f"=== GROUPED INTO {len(records_by_malk)} RECORDS ===")
            for malk, xml_types in records_by_malk.items():
                print(f"Record {malk}: {list(xml_types.keys())}")

            # Step 2: Process each record (maLK) with its own timestamp
            for ma_lk_value, xml_types_data in records_by_malk.items():
                # Tạo timestamp riêng cho từng hồ sơ (maLK) - CHỈ TẠO MỘT LẦN
                record_datetime = std_datetime.now()
                record_timestamp = record_datetime.strftime('%Y%m%d%H%M')
                
                print(f"\n=== PROCESSING RECORD MaLK: {ma_lk_value} ===")
                print(f"FIXED TIMESTAMP FOR THIS RECORD: {record_timestamp}")
                print(f"XML Types in this record: {list(xml_types_data.keys())}")
                
                # Process all XML types for this record with the SAME timestamp
                for xml_type_key, items_list in xml_types_data.items():
                    print(f"\n  Processing {xml_type_key} for maLK {ma_lk_value}")
                    print(f"  Using timestamp: {record_timestamp}")
                    
                    current_type_saved = 0
                    current_type_updated = 0
                    
                    for item_info in items_list:
                        model_class = item_info['model_class']
                        item_data = item_info['item_data']
                        
                        print(f"    Processing {model_class.__name__} (has ngayChinhSua: {model_class in MODELS_WITH_NGAY_CHINH_SUA})")
                        
                        try:
                            with transaction.atomic():
                                # Tạo bản sao để không thay đổi item_data gốc
                                current_item_data = item_data.copy()
                                
                                # Remove auto-managed fields from input data
                                current_item_data.pop('id', None)
                                
                                print(f"    Processing {model_class.__name__} item with timestamp: {record_timestamp}")
                                
                                # Prepare data for model creation với FIXED record timestamp
                                cleaned_data = prepare_model_data(model_class, current_item_data, record_timestamp)
                                
                                # VERIFY timestamp is correct before saving
                                actual_ngay_tao = cleaned_data.get('ngayTao', 'NOT_SET')
                                actual_ngay_chinh_sua = cleaned_data.get('ngayChinhSua', 'NOT_APPLICABLE')
                                
                                print(f"    FINAL CHECK - {xml_type_key} MaLK: {ma_lk_value}")
                                print(f"      Expected timestamp: {record_timestamp}")
                                print(f"      Actual ngayTao: {actual_ngay_tao}")
                                print(f"      Actual ngayChinhSua: {actual_ngay_chinh_sua}")
                                
                                if actual_ngay_tao != record_timestamp:
                                    print(f"    WARNING: ngayTao mismatch! Expected: {record_timestamp}, Got: {actual_ngay_tao}")
                                    cleaned_data['ngayTao'] = record_timestamp  # Force correct timestamp
                                
                                # Only check ngayChinhSua for models that should have it
                                if model_class in MODELS_WITH_NGAY_CHINH_SUA:
                                    if actual_ngay_chinh_sua != record_timestamp:
                                        print(f"    WARNING: ngayChinhSua mismatch! Expected: {record_timestamp}, Got: {actual_ngay_chinh_sua}")
                                        cleaned_data['ngayChinhSua'] = record_timestamp  # Force correct timestamp
                                
                                # Save instance with special handling for models that override save()
                                save_success = save_instance_with_fixed_timestamp(model_class, cleaned_data, record_timestamp)
                                
                                if save_success:
                                    current_type_saved += 1
                                    saved_info = f"ngayTao: {record_timestamp}"
                                    if model_class in MODELS_WITH_NGAY_CHINH_SUA:
                                        saved_info += f", ngayChinhSua: {record_timestamp}"
                                    print(f"    SAVED: {model_class.__name__} with {saved_info}")

                        except (IntegrityError, DataError) as e_db:
                            error_detail = str(e_db)
                            
                            # Try to extract problematic field from error message
                            problem_field_match = re.search(r"column '(\w+)'", error_detail)
                            problem_field = problem_field_match.group(1) if problem_field_match else 'unknown field'
                            
                            all_error_messages.append(f"Lỗi CSDL ({xml_type_key} - MaLK: {ma_lk_value} - Trường: {problem_field}): {error_detail}")
                            print(f"    ERROR: Database error for {xml_type_key} - {error_detail}")
                            
                        except (ValueError, TypeError) as e_type:
                            all_error_messages.append(f"Lỗi kiểu dữ liệu ({xml_type_key} - MaLK: {ma_lk_value}): {str(e_type)}")
                            print(f"    ERROR: Type error for {xml_type_key} - {str(e_type)}")
                            
                        except Exception as e_item:
                            all_error_messages.append(f"Lỗi lưu ({xml_type_key} - MaLK: {ma_lk_value}): {str(e_item)}")
                            print(f"    ERROR: General error for {xml_type_key} - {str(e_item)}")
                    
                    total_saved_count += current_type_saved
                    total_updated_count += current_type_updated
                    
                    if current_type_saved > 0 or current_type_updated > 0:
                        # Check if this XML type is already in summary
                        existing_summary = None
                        for i, summary in enumerate(processed_types_summary):
                            if summary.startswith(xml_type_key):
                                existing_summary = i
                                break
                        
                        if existing_summary is not None:
                            # Update existing summary
                            old_summary = processed_types_summary[existing_summary]
                            # Extract current counts from old summary
                            import re
                            saved_match = re.search(r'Lưu mới: (\d+)', old_summary)
                            updated_match = re.search(r'Cập nhật: (\d+)', old_summary)
                            old_saved = int(saved_match.group(1)) if saved_match else 0
                            old_updated = int(updated_match.group(1)) if updated_match else 0
                            
                            new_saved = old_saved + current_type_saved
                            new_updated = old_updated + current_type_updated
                            processed_types_summary[existing_summary] = f"{xml_type_key} (Lưu mới: {new_saved}, Cập nhật: {new_updated})"
                        else:
                            # Add new summary
                            processed_types_summary.append(f"{xml_type_key} (Lưu mới: {current_type_saved}, Cập nhật: {current_type_updated})")
                
                print(f"=== COMPLETED RECORD MaLK: {ma_lk_value} with timestamp: {record_timestamp} ===")
            
            # Prepare response message
            summary_str = "; ".join(processed_types_summary) if processed_types_summary else "Không có dữ liệu nào được xử lý."
            
            if all_error_messages:
                error_str = "; ".join(all_error_messages[:5])  # Limit error messages to avoid too long response
                if len(all_error_messages) > 5:
                    error_str += f" và {len(all_error_messages) - 5} lỗi khác..."
                    
                return JsonResponse({
                    'success': False, 
                    'message': f"Hoàn thành xử lý nhóm {xml_group_processed}. {summary_str}. Lỗi: {error_str}",
                    'total_saved': total_saved_count,
                    'total_updated': total_updated_count,
                    'error_count': len(all_error_messages),
                    'records_processed': len(records_by_malk)
                })
            
            return JsonResponse({
                'success': True,
                'message': f"Đã xử lý thành công nhóm {xml_group_processed}. {summary_str}.",
                'total_saved': total_saved_count,
                'total_updated': total_updated_count,
                'records_processed': len(records_by_malk)
            })

        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Dữ liệu gửi lên không phải là JSON hợp lệ.'}, status=400)
        except Exception as e:
            return JsonResponse({'success': False, 'message': f'Lỗi máy chủ khi lưu dữ liệu: {str(e)}'}, status=500)

    return JsonResponse({'success': False, 'message': 'Yêu cầu không hợp lệ (chỉ chấp nhận POST).'}, status=405)

@login_required
def edit_xml_view(request, ma_lk):
    """
    View để render trang chỉnh sửa tổng hợp (có thể dùng cho trang riêng hoặc modal)
    Hiện tại được dùng để render template cho modal.
    """
    context = {
        'maLK_from_url': ma_lk,
        'db_data_map_json': '{}' # Mặc định là JSON rỗng
    }

    # View này chỉ cần render template chứa modal.
    # Dữ liệu sẽ được tải bởi AJAX call trong openEditPreviewModal.
    # Không cần fetch dữ liệu ở đây nữa.
    return render(request, 'xml4750/xml_edit_4750_modal.html', context) # Render template chứa modal

@login_required
def api_get_hoso_by_malk_view(request, ma_lk, ngayTao=None):
    """
    API endpoint để lấy toàn bộ dữ liệu của một hồ sơ (tất cả XML types) theo maLK và ngayTao từ CSDL.
    BẮTT BUỘC phải có cả maLK và ngayTao thì mới lấy được dữ liệu.
    
    Parameters:
    - ma_lk: Mã liên kết (từ URL)
    - ngayTao: Ngày tạo (từ URL parameter hoặc query parameter, format: yyyyMMddHHmm)
    """
    if request.method != 'GET':
        return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ.'}, status=405)

    if not ma_lk:
        return JsonResponse({'success': False, 'message': 'Thiếu Mã Liên Kết.'}, status=400)

    # Lấy ngayTao từ URL parameter hoặc fallback sang query parameter
    ngay_tao = ngayTao or request.GET.get('ngayTao')
    if not ngay_tao:
        return JsonResponse({'success': False, 'message': 'Thiếu thông tin ngày tạo (ngayTao).'}, status=400)

    # Validate format ngayTao (should be yyyyMMddHHmm - 12 digits)
    if not (isinstance(ngay_tao, str) and len(ngay_tao) == 12 and ngay_tao.isdigit()):
        return JsonResponse({'success': False, 'message': 'Định dạng ngày tạo không hợp lệ. Yêu cầu: yyyyMMddHHmm (12 chữ số).'}, status=400)

    db_data_map = {}
    data_found_for_malk = False
    debug_info = []
    
    print(f"\n=== API GET HOSO DEBUG ===")
    print(f"Requested maLK: {ma_lk}")
    print(f"Requested ngayTao: {ngay_tao}")
    print(f"ngayTao type: {type(ngay_tao)}")

    # Helper function to serialize field values properly for JSON
    def json_converter(o):
        if isinstance(o, (std_date, std_datetime)):
            return o.isoformat()
        if isinstance(o, decimal.Decimal):
            return str(o)
        raise TypeError(f"Object of type {o.__class__.__name__} is not JSON serializable")

    try:
        for xml_key, model_class in XML_MODEL_MAP.items():
            print(f"\n--- Processing {xml_key} ({model_class.__name__}) ---")
            
            # Kiểm tra xem model có trường ngayTao và maLK không
            has_ngay_tao_field = False
            has_ma_lk_field = False
            ngay_tao_field_type = None
            
            try:
                ngay_tao_field = model_class._meta.get_field('ngayTao')
                has_ngay_tao_field = True
                ngay_tao_field_type = type(ngay_tao_field).__name__
                print(f"  ngayTao field type: {ngay_tao_field_type}")
            except FieldDoesNotExist:
                print(f"  No ngayTao field found")
                pass
            
            try:
                model_class._meta.get_field('maLK')
                has_ma_lk_field = True
                print(f"  Has maLK field: True")
            except FieldDoesNotExist:
                print(f"  Has maLK field: False")
                pass

            # Debug info
            debug_info.append(f"{xml_key}: has_ngayTao={has_ngay_tao_field}, has_maLK={has_ma_lk_field}, ngayTao_field_type={ngay_tao_field_type}")

            # BẮT BUỘC phải có cả hai trường maLK và ngayTao
            if not has_ma_lk_field:
                debug_info.append(f"{xml_key}: Bỏ qua - không có trường maLK")
                print(f"  SKIPPED: No maLK field")
                continue
                
            if not has_ngay_tao_field:
                debug_info.append(f"{xml_key}: Bỏ qua - không có trường ngayTao")
                print(f"  SKIPPED: No ngayTao field")
                continue

            # Kiểm tra dữ liệu hiện có trong database cho maLK này
            print(f"  Checking existing data for maLK: {ma_lk}")
            
            # First, get all records with this maLK to see what ngayTao values exist
            all_records_for_malk = model_class.objects.filter(maLK=ma_lk)
            existing_ngay_tao_values = list(all_records_for_malk.values_list('ngayTao', flat=True).distinct())
            
            print(f"  Total records with maLK {ma_lk}: {all_records_for_malk.count()}")
            print(f"  Existing ngayTao values: {existing_ngay_tao_values}")
            
            # Check if our requested ngayTao exists
            ngay_tao_exists = ngay_tao in [str(val) for val in existing_ngay_tao_values]
            print(f"  Requested ngayTao {ngay_tao} exists: {ngay_tao_exists}")
            
            debug_info.append(f"{xml_key}: Total records with maLK={all_records_for_malk.count()}, existing ngayTao values={existing_ngay_tao_values}")

            # Chỉ xử lý khi model có CẢ HAI trường maLK và ngayTao
            debug_info.append(f"{xml_key}: Filter với maLK={ma_lk} và ngayTao={ngay_tao}")

            # Try different approaches to filter by ngayTao
            records = []
            
            # Method 1: Direct string comparison
            print(f"  Method 1: Direct string filter")
            try:
                records_method1 = list(model_class.objects.filter(
                    maLK=ma_lk,
                    ngayTao=ngay_tao
                ).values())
                print(f"    Method 1 result: {len(records_method1)} records")
                if records_method1:
                    records = records_method1
            except Exception as e1:
                print(f"    Method 1 failed: {e1}")
            
            # Method 2: If no results, try with exact string match using __exact
            if not records:
                print(f"  Method 2: Exact string filter")
                try:
                    records_method2 = list(model_class.objects.filter(
                        maLK=ma_lk,
                        ngayTao__exact=ngay_tao
                    ).values())
                    print(f"    Method 2 result: {len(records_method2)} records")
                    if records_method2:
                        records = records_method2
                except Exception as e2:
                    print(f"    Method 2 failed: {e2}")
            
            # Method 3: If still no results, try converting to int if field is IntegerField
            if not records and ngay_tao_field_type in ['IntegerField', 'BigIntegerField']:
                print(f"  Method 3: Integer conversion filter")
                try:
                    ngay_tao_int = int(ngay_tao)
                    records_method3 = list(model_class.objects.filter(
                        maLK=ma_lk,
                        ngayTao=ngay_tao_int
                    ).values())
                    print(f"    Method 3 result: {len(records_method3)} records")
                    if records_method3:
                        records = records_method3
                except Exception as e3:
                    print(f"    Method 3 failed: {e3}")
            
            # Method 4: Raw SQL query for debugging
            if not records:
                print(f"  Method 4: Raw SQL debugging")
                try:
                    from django.db import connection
                    table_name = model_class._meta.db_table
                    
                    with connection.cursor() as cursor:
                        # Check what the actual data looks like
                        cursor.execute(f"SELECT maLK, ngayTao FROM {table_name} WHERE maLK = %s LIMIT 5", [ma_lk])
                        sample_data = cursor.fetchall()
                        print(f"    Sample data from {table_name}: {sample_data}")
                        
                        # Try exact match with raw SQL
                        cursor.execute(f"SELECT * FROM {table_name} WHERE maLK = %s AND ngayTao = %s", [ma_lk, ngay_tao])
                        raw_results = cursor.fetchall()
                        print(f"    Raw SQL result count: {len(raw_results)}")
                        
                        if raw_results:
                            # Convert raw results to dict format
                            columns = [col[0] for col in cursor.description]
                            records = [dict(zip(columns, row)) for row in raw_results]
                            print(f"    Converted to dict format: {len(records)} records")
                        
                except Exception as e4:
                    print(f"    Method 4 failed: {e4}")
            
            debug_info.append(f"{xml_key}: Final result - {len(records)} records found")
            print(f"  FINAL RESULT: {len(records)} records found")
            
            if records:
                db_data_map[xml_key] = records
                data_found_for_malk = True
                print(f"  Added {len(records)} records to result")
                
                # Log first record for verification
                if records:
                    first_record = records[0]
                    print(f"  First record maLK: {first_record.get('maLK')}, ngayTao: {first_record.get('ngayTao')}")

        print(f"\n=== FINAL SUMMARY ===")
        print(f"Total XML types with data: {len(db_data_map)}")
        print(f"Data found for maLK: {data_found_for_malk}")

        # Thêm debug info vào response nếu không tìm thấy dữ liệu
        if not data_found_for_malk:
            return JsonResponse({
                'success': True, 
                'data': {}, 
                'message': f'Không tìm thấy dữ liệu cho Mã Liên Kết: {ma_lk} và Ngày Tạo: {ngay_tao}',
                'debug_info': debug_info,
                'search_criteria': {
                    'maLK': ma_lk,
                    'ngayTao': ngay_tao,
                    'ngayTao_type': type(ngay_tao).__name__
                }
            }, json_dumps_params={'default': json_converter, 'ensure_ascii': False})

        return JsonResponse({
            'success': True, 
            'data': db_data_map,
            'message': f'Đã tìm thấy dữ liệu cho Mã Liên Kết: {ma_lk} và Ngày Tạo: {ngay_tao}',
            'debug_info': debug_info,
            'search_criteria': {
                'maLK': ma_lk,
                'ngayTao': ngay_tao,
                'ngayTao_type': type(ngay_tao).__name__
            },
            'summary': {
                'xml_types_found': len(db_data_map),
                'total_records': sum(len(records) for records in db_data_map.values())
            }
        }, json_dumps_params={'default': json_converter, 'ensure_ascii': False})

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False, 
            'message': f'Lỗi máy chủ khi lấy dữ liệu hồ sơ: {str(e)}',
            'debug_info': debug_info,
            'search_criteria': {
                'maLK': ma_lk,
                'ngayTao': ngay_tao,
                'ngayTao_type': type(ngay_tao).__name__
            }
        }, status=500)

@login_required
def save_edited_data_from_modal(request):
    """
    API endpoint để lưu toàn bộ dữ liệu của một hồ sơ (tất cả XML types) từ modal chỉnh sửa vào CSDL.
    Được gọi bởi AJAX từ modal chỉnh sửa khi dữ liệu nguồn là CSDL.
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ.'}, status=405)

    # Lấy thông tin từ request
    original_maLK = request.POST.get('original_maLK')
    current_maLK = request.POST.get('current_maLK')
    maLK_changed = request.POST.get('maLK_changed', 'false').lower() == 'true'
    edited_data_map_json = request.POST.get('edited_data_map')

    if not original_maLK or not current_maLK or not edited_data_map_json:
        return JsonResponse({'success': False, 'message': 'Thiếu Mã Liên Kết hoặc dữ liệu chỉnh sửa.'}, status=400)

    try:
        edited_data_map = json.loads(edited_data_map_json)
        if not isinstance(edited_data_map, dict):
            return JsonResponse({'success': False, 'message': 'Dữ liệu chỉnh sửa không hợp lệ.'}, status=400)

        with transaction.atomic():
            # Nếu maLK thay đổi, xóa tất cả dữ liệu cũ trước
            if maLK_changed and original_maLK != current_maLK:
                for xml_type_key in ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15']:
                    model_class = XML_MODEL_MAP.get(xml_type_key)
                    if model_class and hasattr(model_class, 'maLK'):
                        deleted_count = model_class.objects.filter(maLK=original_maLK).delete()[0]
                        if deleted_count > 0:
                            print(f"Deleted {deleted_count} records from {xml_type_key} with maLK {original_maLK}")

            # Xử lý từng loại XML
            for xml_type_key, data_list_for_type in edited_data_map.items():
                model_class = XML_MODEL_MAP.get(xml_type_key)
                if not model_class:
                    continue

                print(f"Processing {xml_type_key} with {len(data_list_for_type)} records")

                # Kiểm tra xem có dữ liệu thực sự không (không chỉ có maLK)
                has_meaningful_data = False
                for item_data in data_list_for_type:
                    for key, value in item_data.items():
                        if key != 'maLK' and value is not None and value != '':
                            has_meaningful_data = True
                            break
                    if has_meaningful_data:
                        break

                # Nếu không có dữ liệu có nghĩa, bỏ qua XML type này
                if not has_meaningful_data:
                    print(f"Skipping {xml_type_key} - no meaningful data (only maLK)")
                    continue

                # Nếu maLK không thay đổi, chỉ xóa dữ liệu của XML type hiện tại
                if not maLK_changed and hasattr(model_class, 'maLK'):
                    deleted_count = model_class.objects.filter(maLK=current_maLK).delete()[0]
                    print(f"Deleted {deleted_count} existing records from {xml_type_key}")

                # Tạo dữ liệu mới
                for item_index, item_data in enumerate(data_list_for_type):
                    try:
                        cleaned_item_data = {}
                        for key, value in item_data.items():
                            if hasattr(model_class, key):
                                cleaned_item_data[key] = value

                        # Đảm bảo maLK được set đúng
                        cleaned_item_data['maLK'] = current_maLK

                        # Xử lý các field bắt buộc
                        for field_obj in model_class._meta.get_fields():
                            field_name = field_obj.name

                            # Bỏ qua các field tự động
                            if field_name in ['id', 'ngayTao', 'ngayChinhSua']:
                                continue

                            # Xử lý field số
                            if isinstance(field_obj, (models.FloatField, models.IntegerField, models.DecimalField)):
                                if field_name in cleaned_item_data:
                                    if cleaned_item_data[field_name] is None or cleaned_item_data[field_name] == '':
                                        if not field_obj.null:
                                            cleaned_item_data[field_name] = field_obj.default if hasattr(field_obj, 'default') and field_obj.default is not None else 0
                                        else:
                                            cleaned_item_data[field_name] = None

                            # Xử lý field text - chỉ bắt buộc maLK
                            elif isinstance(field_obj, (models.CharField, models.TextField)):
                                if field_name == 'maLK':
                                    # maLK là bắt buộc
                                    if field_name not in cleaned_item_data or cleaned_item_data[field_name] is None or cleaned_item_data[field_name] == '':
                                        print(f"Skipping record {item_index} in {xml_type_key} - missing required field: {field_name}")
                                        break
                                else:
                                    # Các field khác không bắt buộc, set empty string nếu null và field không cho phép null
                                    if not field_obj.null and field_name in cleaned_item_data:
                                        if cleaned_item_data[field_name] is None or cleaned_item_data[field_name] == '':
                                            cleaned_item_data[field_name] = ''
                        else:
                            # Tạo record mới (chỉ khi không break ở loop trên)
                            model_class.objects.create(**cleaned_item_data)
                            print(f"Created record in {xml_type_key}")

                    except Exception as field_error:
                        print(f"Error creating record {item_index} in {xml_type_key}: {str(field_error)}")
                        raise Exception(f"Lỗi tại {xml_type_key} record {item_index + 1}: {str(field_error)}")

        return JsonResponse({'success': True, 'message': f'Đã lưu dữ liệu cho hồ sơ {current_maLK} thành công.'})

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Dữ liệu chỉnh sửa không phải là JSON hợp lệ.'}, status=400)
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'success': False, 'message': f'Lỗi máy chủ khi lưu dữ liệu chỉnh sửa: {str(e)}'}, status=500)

@login_required
def delete_selected_records(request, xml_type):
    """
    API endpoint để xóa nhiều records theo ID cho XML types khác (không phải XML0/XML1)
    """
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Phương thức không được hỗ trợ.'}, status=405)

    selected_rows_json = request.POST.get('selected_rows')
    if not selected_rows_json:
        return JsonResponse({'success': False, 'message': 'Thiếu danh sách ID để xóa.'}, status=400)

    try:
        selected_ids = json.loads(selected_rows_json)
        if not isinstance(selected_ids, list) or not selected_ids:
            return JsonResponse({'success': False, 'message': 'Danh sách ID không hợp lệ.'}, status=400)

        # Get model based on XML type
        model_class = XML_MODEL_MAP.get(xml_type.upper())
        if not model_class:
            return JsonResponse({'success': False, 'message': f"Không tìm thấy loại XML: {xml_type}"}, status=400)

        with transaction.atomic():
            # Xóa các records theo ID
            deleted_count, _ = model_class.objects.filter(id__in=selected_ids).delete()

        return JsonResponse({
            'success': True,
            'message': f'Đã xóa thành công {deleted_count} bản ghi từ {xml_type}.',
            'deleted_count': deleted_count
        })

    except json.JSONDecodeError:
        return JsonResponse({'success': False, 'message': 'Danh sách ID không phải là JSON hợp lệ.'}, status=400)
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'success': False, 'message': f'Lỗi máy chủ khi xóa dữ liệu: {str(e)}'}, status=500)


@login_required
def export_excel(request):
    """
    Xuất dữ liệu XML4750 ra file Excel với tất cả các trường từ database
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        # Parse filters từ request
        filters_json = request.POST.get('filters', '{}')
        filters = json.loads(filters_json)

        # Tạo workbook mới
        wb = openpyxl.Workbook()

        # Xóa sheet mặc định
        wb.remove(wb.active)

        # Danh sách các model cần xuất (XML0-XML15)
        xml_models = [
            ('XML0', XML0Model),
            ('XML1', XML1Model),
            ('XML2', XML2Model),
            ('XML3', XML3Model),
            ('XML4', XML4Model),
            ('XML5', XML5Model),
            ('XML6', XML6Model),
            ('XML7', XML7Model),
            ('XML8', XML8Model),
            ('XML9', XML9Model),
            ('XML10', XML10Model),
            ('XML11', XML11Model),
            ('XML12', XML12Model),
            ('XML13', XML13Model),
            ('XML14', XML14Model),
            ('XML15', XML15Model),
        ]

        for sheet_name, model_class in xml_models:
            # Tạo queryset với filters
            queryset = model_class.objects.all()

            # Áp dụng filters
            if filters.get('ma_lien_ket'):
                queryset = queryset.filter(maLK__icontains=filters['ma_lien_ket'])
            if filters.get('ho_ten'):
                queryset = queryset.filter(hoTen__icontains=filters['ho_ten'])
            if filters.get('ma_the_bhyt'):
                queryset = queryset.filter(maTheBHYT__icontains=filters['ma_the_bhyt'])
            if filters.get('ngay_vao_from'):
                queryset = queryset.filter(ngayVao__gte=filters['ngay_vao_from'])
            if filters.get('ngay_vao_to'):
                queryset = queryset.filter(ngayVao__lte=filters['ngay_vao_to'])
            if filters.get('ma_benh_chinh'):
                queryset = queryset.filter(maBenhChinh__icontains=filters['ma_benh_chinh'])
            if filters.get('ma_cskcb'):
                queryset = queryset.filter(maCSKCB__icontains=filters['ma_cskcb'])

            # Lấy dữ liệu
            data = list(queryset.values())

            if data:
                # Tạo DataFrame
                df = pd.DataFrame(data)

                # Tạo worksheet
                ws = wb.create_sheet(title=sheet_name)

                # Thêm header
                headers = list(df.columns)
                for col_num, header in enumerate(headers, 1):
                    cell = ws.cell(row=1, column=col_num, value=header)
                    cell.font = Font(bold=True)
                    cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                    cell.alignment = Alignment(horizontal="center")

                # Thêm dữ liệu
                for row_num, row_data in enumerate(data, 2):
                    for col_num, header in enumerate(headers, 1):
                        value = row_data.get(header, '')

                        # Format datetime fields
                        if isinstance(value, (std_datetime, std_date)):
                            value = value.strftime('%d/%m/%Y %H:%M') if isinstance(value, std_datetime) else value.strftime('%d/%m/%Y')

                        # Format decimal fields
                        elif isinstance(value, decimal.Decimal):
                            value = float(value)

                        ws.cell(row=row_num, column=col_num, value=value)

                # Auto-adjust column widths
                for column in ws.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    ws.column_dimensions[column_letter].width = adjusted_width

        # Nếu không có sheet nào được tạo, tạo sheet trống
        if not wb.worksheets:
            ws = wb.create_sheet(title="No Data")
            ws.cell(row=1, column=1, value="Không có dữ liệu phù hợp với bộ lọc")

        # Tạo response
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = f'attachment; filename="XML4750_Database_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'

        # Lưu workbook vào response
        wb.save(response)

        return response

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'error': f'Lỗi khi xuất Excel: {str(e)}'}, status=500)

# Validation functions
@login_required
def validation_config(request):
    """Hiển thị trang cấu hình kiểm tra dữ liệu"""
    return render(request, 'xml4750/validation_config.html')

@login_required
def get_field_config(request):
    """API endpoint để lấy cấu hình của một trường cụ thể"""
    xml_type = request.GET.get('xml_type')
    field_name = request.GET.get('field_name')

    if not xml_type or not field_name:
        return JsonResponse({
            'success': False,
            'message': 'Thiếu tham số xml_type hoặc field_name'
        })

    try:
        # Tìm cấu hình cho trường cụ thể
        config = None

        if xml_type in XML_FIELD_CONFIG_MAP:
            field_config_model = XML_FIELD_CONFIG_MAP[xml_type]
            try:
                field_config = field_config_model.objects.get(field_name=field_name)
                config = {
                    'field': field_config.field_name,
                    'xmlType': xml_type,
                    'dataType': field_config.data_type,
                    'compareType': field_config.compare_type,
                    'sizeValue': field_config.size_value,
                    'required': field_config.required,
                    'warning': field_config.warning,
                    'blocking': field_config.blocking
                }
            except field_config_model.DoesNotExist:
                pass

        if config:
            return JsonResponse({
                'success': True,
                'config': config
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Không tìm thấy cấu hình cho trường này'
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def save_field_config(request):
    """API endpoint để lưu cấu hình trường"""
    if request.method != 'POST':
        return JsonResponse({
            'success': False,
            'message': 'Phương thức không được hỗ trợ'
        })

    try:
        configs_json = request.POST.get('configs')
        if not configs_json:
            return JsonResponse({
                'success': False,
                'message': 'Không có dữ liệu cấu hình'
            })

        configs = json.loads(configs_json)

        for config in configs:
            xml_type = config.get('xmlType')
            field_name = config.get('field')

            if not xml_type or not field_name:
                continue

            # Lưu cấu hình theo loại XML
            if xml_type in XML_FIELD_CONFIG_MAP:
                field_config_model = XML_FIELD_CONFIG_MAP[xml_type]
                field_config, created = field_config_model.objects.get_or_create(
                    field_name=field_name,
                    defaults={
                        'data_type': config.get('dataType', 'string'),
                        'compare_type': config.get('compareType', 'exact'),
                        'size_value': config.get('sizeValue', ''),
                        'required': config.get('required', False),
                        'warning': config.get('warning', False),
                        'blocking': config.get('blocking', True)
                    }
                )
                if not created:
                    field_config.data_type = config.get('dataType', 'string')
                    field_config.compare_type = config.get('compareType', 'exact')
                    field_config.size_value = config.get('sizeValue', '')
                    field_config.required = config.get('required', False)
                    field_config.warning = config.get('warning', False)
                    field_config.blocking = config.get('blocking', True)
                    field_config.save()

        return JsonResponse({
            'success': True,
            'message': 'Đã lưu cấu hình thành công'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def get_xml_type_config(request):
    """API endpoint để lấy tất cả cấu hình của một loại XML"""
    xml_type = request.GET.get('xml_type')

    if not xml_type:
        return JsonResponse({
            'success': False,
            'message': 'Thiếu tham số xml_type'
        })

    try:
        configs = []

        if xml_type in XML_FIELD_CONFIG_MAP:
            field_config_model = XML_FIELD_CONFIG_MAP[xml_type]
            field_configs = field_config_model.objects.all()
            for field_config in field_configs:
                configs.append({
                    'field': field_config.field_name,
                    'xmlType': xml_type,
                    'dataType': field_config.data_type,
                    'compareType': field_config.compare_type,
                    'sizeValue': field_config.size_value,
                    'required': field_config.required,
                    'warning': field_config.warning,
                    'blocking': field_config.blocking
                })

        return JsonResponse({
            'success': True,
            'configs': configs
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def get_all_field_configs(request):
    """API endpoint để lấy tất cả cấu hình trường"""
    try:
        # Lấy tất cả cấu hình trường từ database
        field_configs = {}

        # Lấy cấu hình từ tất cả XML types
        for xml_type, field_config_model in XML_FIELD_CONFIG_MAP.items():
            for field in field_config_model.objects.all():
                field_configs[f'{xml_type}.{field.field_name}'] = {
                    'field': field.field_name,
                    'xmlType': xml_type,
                    'required': field.required,
                    'dataType': field.data_type,
                    'compareType': field.compare_type,
                    'sizeValue': field.size_value,
                    'warning': field.warning,
                    'blocking': field.blocking
                }
        
        return JsonResponse({
            'success': True,
            'configs': field_configs
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def get_validation_rules(request):
    """API endpoint để lấy tất cả quy tắc kiểm tra"""
    try:
        # Lấy tất cả quy tắc kiểm tra từ database
        rules = []
        validation_rules = ValidationRule.objects.all()
        
        for rule in validation_rules:
            rules.append({
                'id': rule.id,
                'sourceXmlType': rule.source_xml_type,
                'sourceField': rule.source_field,
                'condition': rule.condition,
                'targetXmlType': rule.target_xml_type or '',
                'targetField': rule.target_field or '',
                'compareValue': rule.compare_value or '',
                'errorMessage': rule.error_message,
                'isBlocking': rule.is_blocking,
                'customCondition': rule.custom_condition or ''
            })
        
        return JsonResponse({
            'success': True,
            'rules': rules
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def save_validation_rules(request):
    """API endpoint để lưu quy tắc kiểm tra"""
    if request.method != 'POST':
        return JsonResponse({
            'success': False,
            'message': 'Phương thức không được hỗ trợ'
        })
    
    try:
        # Lấy dữ liệu từ request
        rules_json = request.POST.get('rules')
        if not rules_json:
            return JsonResponse({
                'success': False,
                'message': 'Không có dữ liệu quy tắc'
            })
        
        rules = json.loads(rules_json)
        
        # Xóa tất cả quy tắc cũ
        ValidationRule.objects.all().delete()
        
        # Tạo quy tắc mới
        for rule in rules:
            ValidationRule.objects.create(
                source_xml_type=rule['sourceXmlType'],
                source_field=rule['sourceField'],
                condition=rule['condition'],
                target_xml_type=rule.get('targetXmlType', ''),
                target_field=rule.get('targetField', ''),
                compare_value=rule.get('compareValue', ''),
                error_message=rule['errorMessage'],
                is_blocking=rule['isBlocking'],
                custom_condition=rule.get('customCondition', '')
            )
        
        return JsonResponse({
            'success': True,
            'message': 'Đã lưu quy tắc kiểm tra thành công'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })

@login_required
def get_xml_fields(request):
    """API endpoint để lấy danh sách trường của một loại XML"""
    xml_type = request.GET.get('xml_type')
    
    if not xml_type:
        return JsonResponse({
            'success': False,
            'message': 'Thiếu tham số xml_type'
        })
    
    try:
        from .utils import XML_MODEL_MAP

        fields = []

        # Lấy model tương ứng với xml_type
        if xml_type in XML_MODEL_MAP:
            model_class = XML_MODEL_MAP[xml_type]

            # Lấy tất cả các trường từ model
            for field in model_class._meta.get_fields():
                # Bỏ qua các trường hệ thống
                if field.name in ['id', 'ngayTao', 'ngayChinhSua', 'trangThaiGuiBHXH', 'maTinh']:
                    continue

                # Lấy verbose_name làm description
                description = getattr(field, 'verbose_name', field.name)

                fields.append({
                    'name': field.name,
                    'description': description
                })
        
        return JsonResponse({
            'success': True,
            'fields': fields
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': str(e)
        })
    
# end of validation functions