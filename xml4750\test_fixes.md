# Test Fixes for XML4750 Validation Issues

## 1. <PERSON><PERSON><PERSON> hình kiểm tra cơ bản - Dropdown kiểu so sánh

### Vấn đề:
- <PERSON><PERSON><PERSON> "<PERSON><PERSON><PERSON> so sánh" không có dropdown

### Sửa đổi:
- Thêm `clearable: false` vào editorParams của cột "<PERSON><PERSON><PERSON> so sánh"
- File: `xml4750/templates/xml4750/validation_config.html` dòng 530-539

### Test:
1. Vào trang Cấu hình kiểm tra
2. Click tab "C<PERSON>u hình cơ bản"
3. Click vào cột "<PERSON><PERSON><PERSON> so sánh" của bất kỳ dòng nào
4. Kiểm tra xem có dropdown với các options: <PERSON><PERSON><PERSON> xác, <PERSON><PERSON> dà<PERSON>, <PERSON><PERSON><PERSON><PERSON> gi<PERSON> trị, Mẫu regex, <PERSON><PERSON> sách cho phép, Bỏ qua kiểm tra

## 2. <PERSON><PERSON><PERSON> hình nâng cao - Load đầy đủ XML types và fields

### Vấn đề:
- <PERSON>hông load đầy đủ các loại XML và các cột trong từng XML

### Sửa đổi:
- Thêm XML0 vào dropdown XML nguồn
- Bổ sung XML4, XML5 vào function getFieldsForXMLType
- Thêm XML6-XML15 với cấu trúc cơ bản
- File: `xml4750/templates/xml4750/validation_config.html`

### Test:
1. Vào trang Cấu hình kiểm tra
2. Click tab "Cấu hình nâng cao"
3. Click nút "Thêm điều kiện"
4. Kiểm tra dropdown "XML nguồn" có đầy đủ XML0-XML15
5. Chọn XML1, kiểm tra dropdown "Trường nguồn" có đầy đủ các trường
6. Chọn XML2, XML3, XML4, XML5 và kiểm tra các trường tương ứng

## 3. Edit/Delete buttons trong list_4750.html

### Vấn đề:
- Khi ở dòng 1 chọn edit/delete thì lại thực hiện trên dòng 2

### Sửa đổi:
- Sửa cách tính uniqueRowIdentifier trong action column formatter
- Sử dụng `row.getIndex()` trực tiếp thay vì tính toán phức tạp
- File: `static/js/xml4750/tabulator_xml_tables.js` dòng 924-925

### Test:
1. Vào trang XML4750
2. Upload file XML hoặc load dữ liệu từ database
3. Ở dòng đầu tiên, click nút Edit (biểu tượng bút chì)
4. Kiểm tra xem modal edit có mở với đúng dữ liệu của dòng 1 không
5. Ở dòng đầu tiên, click nút Delete (biểu tượng thùng rác)
6. Kiểm tra xem có confirm delete đúng dòng 1 không

## Các file đã sửa đổi:

1. `xml4750/templates/xml4750/validation_config.html`
   - Thêm clearable: false cho dropdown kiểu so sánh
   - Xóa function loadFieldsForXmlType trùng lặp
   - Thêm XML0, XML4, XML5 vào dropdown
   - Bổ sung XML4-XML15 vào function getFieldsForXMLType

2. `static/js/xml4750/tabulator_xml_tables.js`
   - Sửa cách tính uniqueRowIdentifier trong action column formatter
   - Sử dụng row.getIndex() thay vì tính toán phức tạp

## Cách test tổng thể:

1. Restart server
2. Vào trang XML4750 validation config
3. Test từng tab: Cấu hình cơ bản, Cấu hình nâng cao
4. Vào trang XML4750 list
5. Test edit/delete buttons trên các dòng khác nhau
