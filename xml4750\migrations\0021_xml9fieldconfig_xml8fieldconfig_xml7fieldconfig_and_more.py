# Generated by Django 4.2.7 on 2025-07-06 06:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xml4750', '0020_validationrule_xml3fieldconfig_xml2fieldconfig_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='XML9FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='<PERSON>ểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML9',
                'verbose_name_plural': 'Cấu hình trường XML9',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML8FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML8',
                'verbose_name_plural': 'Cấu hình trường XML8',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML7FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML7',
                'verbose_name_plural': 'Cấu hình trường XML7',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML6FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML6',
                'verbose_name_plural': 'Cấu hình trường XML6',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML5FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML5',
                'verbose_name_plural': 'Cấu hình trường XML5',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML4FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML4',
                'verbose_name_plural': 'Cấu hình trường XML4',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML15FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML15',
                'verbose_name_plural': 'Cấu hình trường XML15',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML14FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML14',
                'verbose_name_plural': 'Cấu hình trường XML14',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML13FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML13',
                'verbose_name_plural': 'Cấu hình trường XML13',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML12FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML12',
                'verbose_name_plural': 'Cấu hình trường XML12',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML11FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML11',
                'verbose_name_plural': 'Cấu hình trường XML11',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML10FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML10',
                'verbose_name_plural': 'Cấu hình trường XML10',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML0FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML0',
                'verbose_name_plural': 'Cấu hình trường XML0',
                'unique_together': {('field_name',)},
            },
        ),
    ]
