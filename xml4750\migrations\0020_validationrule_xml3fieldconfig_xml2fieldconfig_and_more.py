# Generated by Django 4.2.7 on 2025-07-06 04:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xml4750', '0019_xml12model_malk'),
    ]

    operations = [
        migrations.CreateModel(
            name='ValidationRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('source_xml_type', models.CharField(max_length=10, verbose_name='Loại XML nguồn')),
                ('source_field', models.CharField(max_length=100, verbose_name='Trường nguồn')),
                ('condition', models.CharField(choices=[('equal', 'Bằng'), ('not_equal', 'Không bằng'), ('greater', 'Lớn hơn'), ('greater_equal', 'Lớn hơn hoặc bằng'), ('less', 'Nhỏ hơn'), ('less_equal', 'Nhỏ hơn hoặc bằng'), ('contains', 'Chứa'), ('not_contains', 'Không chứa'), ('starts_with', 'Bắt đầu bằng'), ('ends_with', 'Kết thúc bằng'), ('regex', 'Khớp với regex'), ('custom', 'Tùy chỉnh')], max_length=20, verbose_name='Điều kiện')),
                ('target_xml_type', models.CharField(max_length=10, verbose_name='Loại XML đích')),
                ('target_field', models.CharField(max_length=100, verbose_name='Trường đích')),
                ('error_message', models.CharField(blank=True, max_length=255, null=True, verbose_name='Thông báo lỗi')),
                ('is_blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
                ('custom_condition', models.TextField(blank=True, null=True, verbose_name='Điều kiện tùy chỉnh')),
            ],
            options={
                'verbose_name': 'Quy tắc kiểm tra',
                'verbose_name_plural': 'Quy tắc kiểm tra',
            },
        ),
        migrations.CreateModel(
            name='XML3FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML3',
                'verbose_name_plural': 'Cấu hình trường XML3',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML2FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML2',
                'verbose_name_plural': 'Cấu hình trường XML2',
                'unique_together': {('field_name',)},
            },
        ),
        migrations.CreateModel(
            name='XML1FieldConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field_name', models.CharField(max_length=100, verbose_name='Tên trường')),
                ('required', models.BooleanField(default=False, verbose_name='Bắt buộc')),
                ('data_type', models.CharField(choices=[('string', 'Chuỗi'), ('number', 'Số'), ('date', 'Ngày'), ('datetime', 'Ngày giờ'), ('boolean', 'Boolean')], default='string', max_length=20, verbose_name='Kiểu dữ liệu')),
                ('format', models.CharField(blank=True, max_length=255, null=True, verbose_name='Định dạng')),
                ('blocking', models.BooleanField(default=True, verbose_name='Chặn lưu')),
            ],
            options={
                'verbose_name': 'Cấu hình trường XML1',
                'verbose_name_plural': 'Cấu hình trường XML1',
                'unique_together': {('field_name',)},
            },
        ),
    ]
