/**
 * tabulator_xml_tables.js
 * <PERSON><PERSON><PERSON> các định nghĩa bảng và cấu hình cột cho module XML4750
 */

// Hàm tạo cấu hình cột cho Tabulator
function getColumnsConfig(xmlType) {
    var columns = [];
    switch(xmlType) {
        case 'XML0':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true},
                createEditableColumn("Mã bệnh nhân", "maBN", "input"),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Số CCCD", "soCCCD", "input"),
                createEditableColumn("<PERSON><PERSON><PERSON> sinh", "ngaySinh", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Giới tính", "gioiTinh", "list", { headerFilterParams: gioi_tinh,
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(gioi_tinh),
                }, "list"),
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Mã ĐKBD", "maDKBD", "input"),
                { title: "Giá trị thẻ từ", field: "gtTheTu", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate },
                { title: "Giá trị thẻ đến", field: "gtTheDen", headerFilter: true, headerVertical: false, editor: dateEditor, formatter: formatDate},
                createEditableColumn("Mã đối tượng KCB", "maDoiTuongKCB", "list", {
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("doituongkcb"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("doituongkcb");
                        return values[value] || value;
                    }
                }, "list"),
                createEditableColumn("Ngày vào", "ngayVao", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày vào nội trú", "ngayVaoNoiTru", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Lý do vào nội trú", "lyDoVNT", "input"),
                createEditableColumn("Mã lý do vào nội trú", "maLyDoVNT", "list", { headerFilterParams: ma_doi_tuong,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_doi_tuong).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(ma_doi_tuong),
                }, "list"),
                createEditableColumn("Mã loại KCB", "maLoaiKCB", "list", { headerFilterParams: ma_loai_kcb,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_loai_kcb).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(ma_loai_kcb),
                }, "list"),
                createEditableColumn("Mã CSKCB", "maCSKCB", "input"),
                createEditableColumn("Mã dịch vụ", "maDichVu", "input"),
                createEditableColumn("Tên dịch vụ", "tenDichVu", "input"),
                createEditableColumn("Mã thuốc", "maThuoc", "input"),
                createEditableColumn("Tên thuốc", "tenThuoc", "input"),
                createEditableColumn("Mã vật tư", "maVatTu", "input"),
                createEditableColumn("Tên vật tư", "tenVatTu", "input"),
                createEditableColumn("Ngày y lệnh", "ngayYL", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Dự phòng", "duPhong", "input"),
                { title: "Ngày tạo", field: "ngayTao", headerFilter: true, headerVertical: false, formatter: formatDateTime },
                { title: "Ngày chỉnh sửa", field: "ngayChinhSua", headerFilter: true, headerVertical: false, formatter: formatDateTime },
                { title: "TT gửi BHXH", field: "trangThaiGuiBHXH", headerFilter: true, headerVertical: false, formatter: formatKeyValue(trang_thai_gui_hs) },
            ]);
            break;

        case 'XML1':
            // Callback để cập nhật địa chỉ khi mã tỉnh/huyện/xã thay đổi
            const diaChiUpdateCallbackInstance = function(cell) {
                updateDiaChiForRow(cell.getRow());
            };
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Mã bệnh nhân", "maBN", "input"),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Số CCCD", "soCCCD", "input"),
                createEditableColumn("Ngày sinh", "ngaySinh", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Giới tính", "gioiTinh", "list", { headerFilterParams: gioi_tinh,
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(gioi_tinh),
                }, "list"),
                createEditableColumn("Nhóm máu", "nhomMau", "input"),
                createEditableColumn("Mã quốc tịch", "maQuocTich", "input"),
                createEditableColumn("Mã dân tộc", "maDanToc", "list", { headerFilterParams: ma_dan_toc,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_dan_toc).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(ma_dan_toc),
                }, "list"),
                createEditableColumn("Mã nghề nghiệp", "maNgheNghiep", "input"),
                createEditableColumn("Địa chỉ", "diaChi", "input"),
                createEditableColumn("Mã tỉnh cư trú", "maTinhCuTru", "list", {
                    headerFilterParams: getCategoryValues("tinh"),
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("tinh"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("tinh");
                        return values[value] || value;
                    }
                }, "list", diaChiUpdateCallbackInstance), // Pass headerFilter config as object
                createEditableColumn("Mã huyện cư trú", "maHuyenCuTru","list", {
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("quanhuyen"),
                        filterCategory: 'quanhuyen',
                        parentField: 'maTinhCuTru',
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("quanhuyen");
                        return values[value] || value;
                    }
                },  "list",diaChiUpdateCallbackInstance), // Pass headerFilter config as object
                createEditableColumn("Mã xã cư trú", "maXaCuTru", "list", { // Use dynamicListEditor
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("xaphuong"),
                        filterCategory: 'xaphuong',
                        parentField: 'maHuyenCuTru',
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("xaphuong");
                        return values[value] || value;
                    }
                }, "list", diaChiUpdateCallbackInstance), // Pass headerFilter config as object
                createEditableColumn("Số điện thoại", "dienThoai", "input"),
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Mã ĐKBD", "maDKBD", "input"),
                createConditionalDateColumn("Giá trị thẻ từ", "gtTheTu"),
                createConditionalDateColumn("Giá trị thẻ đến", "gtTheDen"),
                createEditableColumn("Ngày miễn CCT", "ngayMienCCT", dateEditor, {formatter: formatDate}),
                createEditableColumn("Lý do vào viện", "lyDoVV", "input"),
                createEditableColumn("Lý do vào nội trú", "lyDoVNT", "input"),
                createEditableColumn("Mã lý do vào nội trú", "maLyDoVNT", "input"),
                createEditableColumn("Chẩn đoán vào", "chanDoanVao", "input"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Mã bệnh chính", "maBenhChinh", "input"),
                createEditableColumn("Mã bệnh kèm theo", "maBenhKT", "input"),
                createEditableColumn("Mã bệnh YHCT", "maBenhYHCT", "input"),
                createEditableColumn("Mã PTTT QT", "maPTTTQT", "input"),
                createEditableColumn("Mã Đối tượng KCB", "maDoiTuongKCB", "input"),
                createEditableColumn("Mã nơi đi", "maNoiDi", "input"),
                createEditableColumn("Mã nơi đến", "maNoiDen", "input"),
                createEditableColumn("Mã tai nạn", "maTaiNan", "list", { 
                    headerFilterParams: getCategoryValues("matainan"),
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("matainan"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("matainan");
                        return values[value] || value;
                    }
                }, "list"),
                createEditableColumn("Ngày vào", "ngayVao", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày vào nội trú", "ngayVaoNoiTru", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày ra", "ngayRa", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Giấy chuyển tuyến", "giayChuyenTuyen", "input"),
                createEditableColumn("Ngày ĐT", "soNgayDtri", "input"),
                createEditableColumn("Phương pháp ĐT", "ppDieuTri", "input"),
                createEditableColumn("Kết quả ĐT", "ketQuaDtri", "list", { 
                    headerFilterParams: getCategoryValues("ketquadieutri"),
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("ketquadieutri"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("ketquadieutri");
                        return values[value] || value;
                    }
                }, "list"),
                createEditableColumn("Mã loại ra viện", "maLoaiRV", "list", { 
                    headerFilterParams: getCategoryValues("loairavien"),
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: getCategoryValues("loairavien"),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: function(cell) {
                        var value = cell.getValue();
                        var values = getCategoryValues("loairavien");
                        return values[value] || value;
                    }
                }, "list"),
                createEditableColumn("Ghi chú", "ghiChu", "input"),
                createEditableColumn("Ngày thanh toán", "ngayTToan", dateTimeEditor, { formatter: formatDateTime }),
                createColumn("Tổng chi thuốc", "tThuoc", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("Tổng chi VTYT", "tVTYT", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("Tổng chi BV", "tTongChiBV", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("Tổng chi BH", "tTongChiBH", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("BNTT", "tBNTT", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("BNCCT", "tBNCCT", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("BHTT", "tBHTT", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("Nguồn khác", "tNguonKhac", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createColumn("BHTT GDV", "tBHTTGDV", { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createEditableColumn("Năm QT", "namQT", "list", { headerFilterParams: nam,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(nam).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(nam),
                }, "list"),
                createEditableColumn("Tháng QT", "thangQT", "list", { headerFilterParams: thang,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(thang).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(thang),
                }, "list"),
                createEditableColumn("Mã loại KCB", "maLoaiKCB", "list", { headerFilterParams: ma_loai_kcb,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_loai_kcb).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(ma_loai_kcb),
                }, "list"),
                createEditableColumn("Mã khoa", "maKhoa", "input"),
                createEditableColumn("Mã CSKCB", "maCSKCB", "input"),
                // createEditableColumn("Mã khu vực", "maKhuVuc", "input"),
                {title:"Mã khu vực", field:"maKhuVuc", editor:"list", editorParams:{values:ma_kv_dict}},
                createEditableColumn("Cân nặng", "canNang", "input"),
                createEditableColumn("Cân nặng con", "canNangCon", "input"),
                createEditableColumn("5 năm LT", "namNamLienTuc", "input"),
                createEditableColumn("Ngày tái khám", "ngayTaiKham", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã HSBA", "maHSBA", "input"),
                createEditableColumn("Mã TTDV", "maTTDV", "input"), 
                createEditableColumn("Dự phòng", "duPhong", "input"),
                { title: "Ngày tạo", field: "ngayTao", headerFilter: true, headerVertical: false, formatter: formatDateTime },
                { title: "Ngày chỉnh sửa", field: "ngayChinhSua", headerFilter: true, headerVertical: false, formatter: formatDateTime },
                { title: "TT gửi BHXH", field: "trangThaiGuiBHXH", headerFilter: true, headerVertical: false, formatter: formatKeyValue(trang_thai_gui_hs) },
            ]);
            break;

        case 'XML2':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false},
                createEditableColumn("Mã thuốc", "maThuoc", "input"),
                createEditableColumn("Mã PP chế biến", "maPPCheBien", "input"),
                createEditableColumn("Mã CSKCB thuốc", "maCSKCBThuoc", "input"),
                createEditableColumn("Mã nhóm", "maNhom", "list", { headerFilterParams: ma_nhom,
                    editorParams: { values: Object.entries(ma_nhom).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(ma_nhom),
                }, "list"),
                createEditableColumn("Tên thuốc", "tenThuoc", "input"),
                createEditableColumn("Đơn vị tính", "donViTinh", "input"),
                createEditableColumn("Hàm lượng", "hamLuong", "input"),
                createEditableColumn("Đường dùng", "duongDung", "list", { headerFilterParams: duong_dung,
                    editorParams: { values: Object.entries(duong_dung).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(duong_dung),
                }, "list"),
                createEditableColumn("Dạng bào chế", "dangBaoChe", "input"),
                createEditableColumn("Liều dùng", "lieuDung", "input"),
                createEditableColumn("Cách dùng", "cachDung", "input"),
                createEditableColumn("Số đăng ký", "soDangKy", "input"),
                createEditableColumn("TT thầu", "ttThau", "input"),
                createEditableColumn("Phạm vi", "phamVi", "list", { headerFilterParams: pham_vi,
                    editorParams: { values: Object.entries(pham_vi).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(pham_vi),
                }, "list"),
                createEditableColumn("Tỷ lệ TT BH", "tyLeTTBH", thousandSeparatorEditor, { 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 0 },
                }),
                createEditableColumn("Số lượng", "soLuong", thousandSeparatorEditor,{ 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createEditableColumn("Đơn giá", "donGia", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Thành tiền BV", "thanhTienBV", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Thành tiền BH", "thanhTienBH", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Nguồn NSNN", "tNguonKhacNSNN", thousandSeparatorEditor,{
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Nguồn VTNN", "tNguonKhacVTNN", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Nguồn VTTN", "tNguonKhacVTTN", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Nguồn còn lại", "tNguonKhacCL", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Nguồn khác", "tNguonKhac", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Mức hưởng", "mucHuong", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "number", formatterParams: {decimal: ".", thousand: ",", precision: 0,},
                }),
                createEditableColumn("BN Thanh toán", "tBNTT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("BN cùng chi trả", "tBNCCT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("BH thanh toán", "tBHTT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Mã khoa", "maKhoa", "input"),
                createEditableColumn("Mã bác sĩ", "maBacSi", "input"),
                createEditableColumn("Mã dịch vụ", "maDichVu", "input"),
                createEditableColumn("Ngày y lệnh", "ngayYL", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày TH y lệnh", "ngayTHYL", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Mã PTTT", "maPTTT", "list", { headerFilterParams: ma_pttt,
                    editorParams: { values: Object.entries(ma_pttt).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(ma_pttt),
                }, "list"),
                createEditableColumn("Nguồn chi trả", "nguonCTra", "list", { headerFilterParams: nguon_ctra,
                    editorParams: { values: Object.entries(nguon_ctra).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(nguon_ctra),
                }, "list"),
                createEditableColumn("Vết thương TP", "vetThuongTP", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML3':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false},
                createEditableColumn("Mã dịch vụ", "maDichVu", "input"),
                createEditableColumn("Mã PTTT QT", "maPTTTQT", "input"),
                createEditableColumn("Mã vật tư", "maVatTu", "input"),
                createEditableColumn("Mã nhóm", "maNhom", "list", { headerFilterParams: ma_nhom,
                    editorParams: { values: Object.entries(ma_nhom).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(ma_nhom),
                }, "list"),
                createEditableColumn("Gói VTYT", "goiVTYT", "input"),
                createEditableColumn("Tên vật tư", "tenVatTu", "input"),
                createEditableColumn("Tên dịch vụ", "tenDichVu", "input"),
                createEditableColumn("Mã xăng dầu", "maXangDau", "input"),
                createEditableColumn("Đơn vị tính", "donViTinh", "input"),
                createEditableColumn("Phạm vi", "phamVi", "list", { headerFilterParams: pham_vi,
                    editorParams: { values: Object.entries(pham_vi).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(pham_vi),
                }, "list"),
                createEditableColumn("Số lượng", "soLuong", thousandSeparatorEditor,{ 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 2 },
                }),
                createEditableColumn("Đơn giá BV", "donGiaBV", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Đơn giá BH", "donGiaBH", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Thành tiền BV", "thanhTienBV", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Thành tiền BH", "thanhTienBH", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("TT thầu", "ttThau", "input"),
                createEditableColumn("Tỷ lệ TT DV", "tyLeTTDV", thousandSeparatorEditor,{ 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 0 },
                }),
                createEditableColumn("Tỷ lệ TT BH", "tyLeTTBH", thousandSeparatorEditor,{ 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 0 },
                }),
                createEditableColumn("Trần TT", "tTranTT", "input"),
                createColumn("Mức hưởng", "mucHuong", thousandSeparatorEditor,{ 
                    headerFilter: true, headerVertical: false, formatter: "money", hozAlign: "right",
                    formatterParams: { decimal: ".", thousand: ",", precision: 0 },
                }),
                createColumn("Nguồn NSNN", "tNguonKhacNSNN", thousandSeparatorEditor,{
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Nguồn VTNN", "tNguonKhacVTNN", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Nguồn VTTN", "tNguonKhacVTTN", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Nguồn còn lại", "tNguonKhacCL", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Nguồn khác", "tNguonKhac", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createColumn("Mức hưởng", "mucHuong", thousandSeparatorEditor, {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "number", formatterParams: {decimal: ".", thousand: ",", precision: 0,},
                }),
                createEditableColumn("BN Thanh toán", "tBNTT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("BN cùng chi trả", "tBNCCT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("BH thanh toán", "tBHTT", {
                    headerFilter: true, headerVertical: false, hozAlign: "right", sorter: "number", formatter: "money", formatterParams: {decimal: ".", thousand: ",", precision: 2,},
                }),
                createEditableColumn("Mã khoa", "maKhoa", "input"),
                createEditableColumn("Mã giường", "maGiuong", "input"),
                createEditableColumn("Mã bác sĩ", "maBacSi", "input"),
                createEditableColumn("Người thực hiện", "nguoiThucHien", "input"),
                createEditableColumn("Mã bệnh", "maBenh", "input"),
                createEditableColumn("Mã bệnh YHCT", "maBenhYHCT", "input"),
                createEditableColumn("Ngày y lệnh", "ngayYL", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày TH y lệnh", "ngayTHYL", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày kết quả", "ngayKQ", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Mã PTTT", "maPTTT", "list", { headerFilterParams: ma_pttt,
                    editorParams: { values: Object.entries(ma_pttt).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(ma_pttt),
                }, "list"),
                createEditableColumn("Vết thương TP", "vetThuongTP", "input"),
                createEditableColumn("PP vô cảm", "ppVoCam", "list", { headerFilterParams: pp_vo_cam,
                    editorParams: { values: Object.entries(pp_vo_cam).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(pp_vo_cam),
                }, "list"),
                createEditableColumn("Vị trí TH DVKT", "viTriThDVKT", "input"),
                createEditableColumn("Mã máy", "maMay", "input"),
                createEditableColumn("Mã hiệu SP", "maHieuSP", "input"),
                createEditableColumn("Tái sử dụng", "taiSuDung", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML4':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false},
                createEditableColumn("Mã dịch vụ", "maDichVu", "input"),
                createEditableColumn("Mã chỉ số", "maChiSo", "input"),
                createEditableColumn("Tên chỉ số", "tenChiSo", "input"),
                createEditableColumn("Giá trị", "giaTri", "input"),
                createEditableColumn("Đơn vị đo", "donViDo", "input"),
                createEditableColumn("Mô tả", "moTa", "input"),
                createEditableColumn("Kết luận", "ketLuan", "input"),
                createEditableColumn("Ngày kết quả", "ngayKQ", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Mã BS đọc KQ", "maBSDocKQ", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML5':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                { title: "STT", field: "stt", headerFilter: true, headerVertical: false},
                createEditableColumn("Diễn biến LS", "dienBienLS", "textarea"),
                createEditableColumn("Giai đoạn bệnh", "giaiDoanBenh", "textarea"),
                createEditableColumn("Hội chẩn", "hoiChan", "textarea"),
                createEditableColumn("Phẫu thuật", "phauThuat", "textarea"),
                createEditableColumn("Thời điểm DBLS", "thoiDiemDBLS", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Người thực hiện", "nguoiThucHien", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML6':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Số CCCD", "soCCCD", "input"),
                createEditableColumn("Ngày sinh", "ngaySinh", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Giới tính", "gioiTinh", "input"),
                createEditableColumn("Địa chỉ", "diaChi", "input"),
                createEditableColumn("Mã tỉnh cư trú", "maTinhCuTru", "input"),
                createEditableColumn("Mã huyện cư trú", "maHuyenCuTru", "input"),
                createEditableColumn("Mã xã cư trú", "maXaCuTru", "input"),
                createEditableColumn("Ngày khẳng định HIV", "ngayKDHIV", dateEditor, { formatter: formatDate }),
                createEditableColumn("Nơi lấy mẫu XN", "noiLayMauXN", "input"),
                createEditableColumn("Nơi XN khẳng định", "noiXNKD", "input"),
                createEditableColumn("Nơi bắt đầu điều trị ARV", "noiBDDTARV", "input"),
                createEditableColumn("Bắt đầu điều trị ARV", "bdDTARV", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã phác đồ điều trị ban đầu", "maPhacDoDieuTriBD", "input"),
                createEditableColumn("Mã bậc phác đồ ban đầu", "maBacPhacDoBD", "input"),
                createEditableColumn("Mã lý do điều trị", "maLydoDtri", "input"),
                createEditableColumn("Loại điều trị lao", "loaiDtriLao", "input"),
                createEditableColumn("Sàng lọc lao", "sangLocLao", "input"),
                createEditableColumn("Phác đồ điều trị lao", "phacDoDtriLao", "input"),
                createEditableColumn("Ngày bắt đầu điều trị lao", "ngayBDDTriLao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày kết thúc điều trị lao", "ngayKTDTriLao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Kết quả điều trị lao", "ketQuaDTriLao", "input"),
                createEditableColumn("Mã lý do xét nghiệm TLVR", "maLydoXNTLVR", "input"),
                createEditableColumn("Ngày xét nghiệm TLVR", "ngayXNTLVR", dateEditor, { formatter: formatDate }),
                createEditableColumn("Kết quả xét nghiệm TLVR", "ketQuaXNTLVR", "input"),
                createEditableColumn("Ngày kết quả xét nghiệm TLVR", "ngayKQXNTLVR", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã loại bệnh nhân", "maLoaiBN", "input"),
                createEditableColumn("Giai đoạn làm sàng", "giaiDoanLamSang", "input"),
                createEditableColumn("Nhóm đối tượng", "nhomDoiTuong", "input"),
                createEditableColumn("Mã tình trạng đăng ký", "maTinhTrangDK", "input"),
                createEditableColumn("Lần xét nghiệm PCR", "lanXNPCR", "input"),
                createEditableColumn("Ngày xét nghiệm PCR", "ngayXNPCR", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày kết quả xét nghiệm PCR", "ngayKQXNPCR", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã kết quả xét nghiệm PCR", "maKQXNPCR", "input"),
                createEditableColumn("Ngày nhận thông tin mang thai", "ngayNhanTTMangThai", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày bắt đầu điều trị ctx", "ngayBatDauDTCTX", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã xử trí", "maXuTri", "input"),
                createEditableColumn("Ngày bắt đầu xử trí", "ngayBatDauXuTri", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày kết thúc xử trí", "ngayKetThucXuTri", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã phác đồ điều trị",  "maPhacDoDieuTri", "input"),
                createEditableColumn("Mã bậc phác đồ", "maBacPhacDo", "input"),
                createEditableColumn("Số ngày cấp thuốc ARV", "soNgayCapThuocARV", "input"),
                createEditableColumn("Ngày chuyển phác đồ", "ngayChuyenPhacDo", dateEditor, { formatter: formatDate }),
                createEditableColumn("Lý do chuyển phác đồ", "lyDoChuyenPhacDo", "input"),
                createEditableColumn("Mã cơ sở KCB", "maCSKCB", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input")
            ]);
            break;

        case 'XML7':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Số lưu trữ", "soLuuTru", "input"),
                createEditableColumn("Mã y tế", "maYTe", "input"),
                createEditableColumn("Mã khoa ra viện", "maKhoaRV", "input"),
                createEditableColumn("Ngày vào", "ngayVao", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Ngày ra", "ngayRa", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Mã định chỉ thai", "maDinhChiThai", "input"),
                createEditableColumn("Nguyên nhân định chỉ", "nguyenNhanDinhChi", "input"),
                createEditableColumn("Thời gian định chỉ", "thoiGianDinhChi", dateTimeEditor, { formatter: formatDateTime }),
                createEditableColumn("Tuổi thai", "tuoiThai", "input"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Phuong pháp điều trị", "ppDieuTri", "input"),
                createEditableColumn("Ghi chú", "ghiChu", "input"),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Mã Bác sĩ", "maBS", "input"),
                createEditableColumn("Tên Bác sĩ", "tenBS", "input"),
                createEditableColumn("Ngày chứng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã cha", "maCha", "input"),
                createEditableColumn("Mã mẹ", "maMe", "input"),
                createEditableColumn("Mã thẻ tạm", "maTheTam", "input"),
                createEditableColumn("Họ tên cha", "hoTenCha", "input"),
                createEditableColumn("Họ tên mẹ", "hoTenMe", "input"),
                createEditableColumn("Số ngày nghỉ", "soNgayNghi", "input"),
                createEditableColumn("Ngoại trú từ ngày", "ngoaiTruTuNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngoại trú đến ngày", "ngoaiTruDenNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML8':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Mã loại KCB", "maLoaiKCB", "input"),
                createEditableColumn("Họ tên cha", "hoTenCha", "input"),
                createEditableColumn("Họ tên mẹ", "hoTenMe", "input"),
                createEditableColumn("Người giám hộ", "nguoiGiamHo", "input"),
                createEditableColumn("Đơn vị", "donVi", "input"),
                createEditableColumn("Ngày vào", "ngayVao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày ra", "ngayRa", dateEditor, { formatter: formatDate }),
                createEditableColumn("Chẩn đoán vào", "chanDoanVao", "textarea"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "textarea"),
                createEditableColumn("Quá trình bệnh lý", "qtBenhLy", "textarea"),
                createEditableColumn("Tóm tắt kết quả", "tomTatKQ", "textarea"),
                createEditableColumn("Phương pháp điều trị", "ppDieuTri", "input"),
                createEditableColumn("Ngày sinh con", "ngaySinhCon", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày con chết", "ngayConChet", dateEditor, { formatter: formatDate }),
                createEditableColumn("Số con chết", "soConChet", "input"),
                createEditableColumn("Kết quả điều trị", "ketQuaDtri", "input"),
                createEditableColumn("Ghi chú", "ghiChu", "input"),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Ngày chứng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã thẻ tạm", "maTheTam", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML9':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Mã BHXH NND", "maBHXHNND", "input"),
                createEditableColumn("Mã thẻ NND", "maTheNND", "input"),
                createEditableColumn("Họ tên NND", "hoTenNND", "input"),
                createEditableColumn("Ngày sinh NND", "ngaySinhNND", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã dân tộc NND", "maDanTocNND", "input"),
                createEditableColumn("Số CCCD NND", "soCCCDNND", "input"),
                createEditableColumn("Ngày cấp CCCD NND", "ngayCapCCCDNND", dateEditor, { formatter: formatDate }),
                createEditableColumn("Nơi cấp CCCD NND", "noiCapCCCDNND", "input"),
                createEditableColumn("Nơi cư trú NND", "noiCuTruNND", "input"),
                createEditableColumn("Mã quốc tịch", "maQuocTich", "input"),
                createEditableColumn("Mã tỉnh cư trú NND", "maTinhCuTruNND", "input"),
                createEditableColumn("Mã huyện cư trú NND", "maHuyenCuTruNND", "input"),
                createEditableColumn("Mã xã cư trú NND", "maXaCuTruNND", "input"),
                createEditableColumn("Họ tên cha", "hoTenCha", "input"),
                createEditableColumn("Mã thẻ tạm", "maTheTam", "input"),
                createEditableColumn("Họ tên con", "hoTenCon", "input"),
                createEditableColumn("Giới tính con", "gioiTinhCon", "input"),
                createEditableColumn("Số con", "soCon", "input"),
                createEditableColumn("Cân nặng con", "canNangCon", "input"),
                createEditableColumn("Ngày sinh con", "ngaySinhCon", dateEditor, { formatter: formatDate }),
                createEditableColumn("Nơi sinh con", "noiSinhCon", "input"),
                createEditableColumn("Tình trạng con", "tinhTrangCon", "input"),
                createEditableColumn("Sinh con phẫu thuật", "sinhConPhauThuat", "input"),
                createEditableColumn("Sinh con dưới 32 tuần", "sinhConDuoi32Tuan", "input"),
                createEditableColumn("Ghi chú", "ghiChu", "input"),
                createEditableColumn("Người đỡ đẻ", "nguoiDoDe", "input"),
                createEditableColumn("Người ghi phiếu", "nguoiGhiPhieu", "input"),
                createEditableColumn("Số chứng từ", "so", "input"),
                createEditableColumn("Quyển số", "quyenSo", "input"),
                createEditableColumn("Ngày chứng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML10':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Số seri", "soSeri", "input"),
                createEditableColumn("Số chứng từ", "soCT", "input"),
                createEditableColumn("Số ngày", "soNgay", "input"),
                createEditableColumn("Đơn vị", "donVi", "input"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Từ ngày", "tuNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Đến ngày", "denNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Tên bác sĩ", "tenBS", "input"),
                createEditableColumn("Mã bác sĩ", "maBS", "input"),
                createEditableColumn("Ngày chưng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML11':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Số chứng từ", "soCT", "input"),
                createEditableColumn("Số seri", "soSeri", "input"),
                createEditableColumn("Số KCB", "soKCB", "input"),
                createEditableColumn("Đơn vị", "donVi", "input"),
                createEditableColumn("Mã BHXH", "maBHXH", "input"),
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Phương pháp điều trị", "ppDieuTri", "input"),
                createEditableColumn("Mã định chỉ thai", "maDinhChiThai", "input"),
                createEditableColumn("Nguyên nhân định chỉ", "nguyenNhanDinhChi", "input"),
                createEditableColumn("Tuổi thai", "tuoiThai", "input"),
                createEditableColumn("Số ngày nghỉ", "soNgayNghi", "input"),
                createEditableColumn("Từ ngày", "tuNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Đến ngày", "denNgay", dateEditor, { formatter: formatDate }),
                createEditableColumn("Họ tên cha", "hoTenCha", "input"),
                createEditableColumn("Họ tên mẹ", "hoTenMe", "input"),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Mã bác sĩ", "maBS", "input"),
                createEditableColumn("Ngày chứng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã thẻ tạm", "maTheTam", "input"),
                createEditableColumn("Mẫu số", "mauSo", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML12':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                createEditableColumn("Người chủ trì", "nguoiChuTri", "input"),
                createEditableColumn("Chức vụ", "chucVu", "input"),
                createEditableColumn("Ngày họp", "ngayHop", dateEditor, { formatter: formatDate }),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Ngày sinh", "ngaySinh", dateEditor, { formatter: formatDate }),
                createEditableColumn("Số CCCD", "soCCCD", "input"),
                createEditableColumn("Ngày cấp CCCD", "ngayCapCCCD", dateEditor, { formatter: formatDate }),
                createEditableColumn("Nơi cấp CCCD", "noiCapCCCD", "input"),
                createEditableColumn("Địa chỉ", "diaChi", "input"),
                createEditableColumn("Mã tỉnh cư trú", "maTinhCuTru", "input"),
                createEditableColumn("Mã huyen cư trú", "maHuyenCuTru", "input"),
                createEditableColumn("Mã xã cư trú", "maXaCuTru", "input"),
                createEditableColumn("Mã BHXH", "maBHXH", "input"),
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Nghề nghiệp", "ngheNghiep", "input"),
                createEditableColumn("Điện thoại", "dienThoai", "input"),
                createEditableColumn("Mã đối tượng", "maDoiTuong", "input"),
                createEditableColumn("Khám giám định", "khamGiamDinh", "input"),
                createEditableColumn("Số biên bản", "soBienBan", "input"),
                createEditableColumn("Tỷ lệ thương tật cũ (%)", "tyLeTTCTCu", "input"),
                createEditableColumn("Dạng hướng chế độ", "dangHuongCheDo", "input"),
                createEditableColumn("Ngày chứng từ", "ngayChungTu", dateEditor, { formatter: formatDate }),
                createEditableColumn("Số giấy giới thiệu", "soGiayGioiThieu", "input"),
                createEditableColumn("Ngày đề nghị", "ngayDeNghi", dateEditor, { formatter: formatDate }),
                createEditableColumn("Mã đơn vị", "maDonVi", "input"),
                createEditableColumn("Giới thiệu của", "gioiThieuCua", "input"),
                createEditableColumn("Kết quả khám", "ketQuaKham", "input"),
                createEditableColumn("Số văn bản căn cứ", "soVanBanCanCu", "input"),
                createEditableColumn("Tỷ lệ thương tật mới (%)", "tyLeTTCTMoi", "input"),
                createEditableColumn("Tổng tỷ lệ thương tật (%)", "tongTyLeTTCT", "input"),
                createEditableColumn("Dạng khuyết tật", "dangkhuyettat", "input"),
                createEditableColumn("Mức độ khuyết tật", "mucDoKhuyetTat", "input"),
                createEditableColumn("Đề nghị", "deNghi", "input"),
                createEditableColumn("Được xác định", "duocXacDinh", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML13':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Số hồ sơ", "soHoSo", "input"),
                createEditableColumn("Số chuyển tuyến", "soChuyenTuyen", "input"),
                createEditableColumn("Giấy chuyển tuyến", "giayChuyenTuyen", "input"),
                createEditableColumn("Mã CSKCB", "maCSKCB", "input"),
                createEditableColumn("Mã nơi đi", "maNoiDi", "input"),
                createEditableColumn("Mã nơi đến", "maNoiDen", "input"),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Ngày sinh", "ngaySinh", dateEditor, { formatter: formatDate }),
                createEditableColumn("Giới tính", "gioiTinh", "list", { headerFilterParams: gioi_tinh,
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(gioi_tinh),
                }, "list"),
                createEditableColumn("Mã quốc tịch", "maQuocTich", "input"),
                createEditableColumn("Mã dân tộc", "maDanToc", "list", { headerFilterParams: ma_dan_toc,
                    editorParams: { 
                        // Sử dụng dữ liệu đã load sẵn
                        values: Object.entries(ma_dan_toc).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})),
                        placeholderEmpty:"Không có kết quả", //set custom placeholder when list is empty
                        autocomplete:true, //enable autocomplete mode,
                        listOnEmpty:true, //show all values in the list if the input is empty
                        filterDelay:100, //delay in milliseconds after typing before filter begins
                        clearable:true, //show clear "x" button on editor
                    },
                    formatter: formatKeyValue(ma_dan_toc),
                }, "list"),
                createEditableColumn("Mã nghề nghiệp", "maNgheNghiep", "input"),
                createEditableColumn("Địa chỉ", "diaChi", "input"),
                createEditableColumn("Mã Thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Giá trị thẻ đến", "gtTheDen", "input"),
                createEditableColumn("Ngày vào", "ngayVao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày vào nội trú", "ngayVaoNoiTru", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày ra", "ngayRa", dateEditor, { formatter: formatDate }),
                createEditableColumn("Dấu hiệu lâm sàng", "dauHieuLS", "input"),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Quá trình bệnh lý", "qtBenhLy", "input"),
                createEditableColumn("Tóm tắt kết quả", "tomtatKQ", "input"),
                createEditableColumn("Phương pháp điều trị", "ppDieuTri", "input"),
                createEditableColumn("Mã bệnh chính", "maBenhChinh", "input"),
                createEditableColumn("Mã bệnh kèm theo", "maBenhKT", "input"),
                createEditableColumn("Mã bệnh YHCT", "maBenhYHCT", "input"),
                createEditableColumn("Mã loại ra viện", "maLoaiRV", "input"),
                createEditableColumn("Mã lý do chuyển", "maLyDoCT", "input"),
                createEditableColumn("Hướng điều trị", "huongDieuTri", "input"),
                createEditableColumn("Phương tiện vận chuyển", "phuongTienVC", "input"),
                createEditableColumn("Họ tên người hộ tống", "hoTenNguoiHT", "input"),
                createEditableColumn("Chức danh người hộ tống", "chucDanhNguoiHT", "input"),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Mã Bác sĩ", "maBS", "input"),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML14':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Số giấy hẹn khám lại", "soGiayHenKL", "input"),
                createEditableColumn("Mã cơ sở KCB", "maCSKCB", "input"),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Ngày sinh", "ngaySinh", dateEditor, { formatter: formatDate }),
                createEditableColumn("Giới tính", "gioiTinh", "list", { headerFilterParams: gioi_tinh,
                    editorParams: { values: Object.entries(gioi_tinh).map(([key, label]) => ({ value: key,label: `${key} - ${label}`})), clearable: false },
                    formatter: formatKeyValue(gioi_tinh),
                }, "list"),
                createEditableColumn("Địa chỉ", "diaChi", "input"),
                createEditableColumn("Mã thẻ BHYT", "maTheBHYT", "input"),
                createEditableColumn("Giá trị thẻ đến", "gtTheDen", "input"),
                createEditableColumn("Ngày vào", "ngayVao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày vào nội trú", "ngayVaoNoiTru", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày ra", "ngayRa", dateEditor, { formatter: formatDate }),
                createEditableColumn("Ngày hẹn khám lại", "ngayHenKL", dateEditor, { formatter: formatDate }),
                createEditableColumn("Chẩn đoán ra viện", "chanDoanRV", "input"),
                createEditableColumn("Mã bệnh chính", "maBenhChinh", "input"),
                createEditableColumn("Mã bệnh kèm theo", "maBenhKT", "input"),
                createEditableColumn("Mã bệnh YHCT", "maBenhYHCT", "input"),
                createEditableColumn("Mã đối tượng KCB", "maDoiTuongKCB", "input"),
                createEditableColumn("Mã Bác sĩ", "maBacSi", "input"),
                createEditableColumn("Mã Thủ trưởng đơn vị", "maTTDV", "input"),
                createEditableColumn("Ngày chứng từ", "ngayCT", dateEditor, { formatter: formatDate }),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;

        case 'XML15':
            columns = columns.concat([
                { title:"Chọn", formatter:"rowSelection", titleFormatter:"rowSelection", hozAlign:"center", headerSort:false, frozen:true, width:40},
                { title: "Mã liên kết", field: "maLK", headerFilter: true, headerVertical: false, frozen:true },
                createEditableColumn("Mã bệnh nhân", "maBN", "input"),
                createEditableColumn("Họ tên", "hoTen", "input"),
                createEditableColumn("Số CCCD", "soCCCD", "input"),
                createEditableColumn("Phân loại lao vị trí", "phanLoaiLaoViTri", "input"),
                createEditableColumn("Phân loại lao tiền sử", "phanLoaiLaoTS", "input"),
                createEditableColumn("Phân loại lao HIV", "phanLoaiLaoHIV", "input"),
                createEditableColumn("Phân loại lao vi khuẩn", "phanLoaiLaoVK", "input"),
                createEditableColumn("Phân loại lao kèm theo", "phanLoaiLaoKT", "input"),
                createEditableColumn("Loại điều trị lao", "loaiDTriLao", "input"),
                createEditableColumn("Phác đồ điều trị lao", "phacDoDTriLao", "input"),
                createEditableColumn("Ngày bắt đầu điều trị lao", "ngayBDDTriLao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Phác đồ điều trị Lao", "phacDoDTriLao", "input"),
                createEditableColumn("Ngày kết thúc điều trị lao", "ngayKTDTriLao", dateEditor, { formatter: formatDate }),
                createEditableColumn("Kết quả điều trị lao", "ketQuaDTriLao", "input"),
                createEditableColumn("Mã CSKCB", "maCSKCB", "input"),
                createEditableColumn("Ngày khẳng định HIV", "ngayKDHIV", dateEditor, { formatter: formatDate }),
                createEditableColumn("Bắt đầu điều trị ARV", "bddtARV", "input"),
                createEditableColumn("Ngày bắt đầu điều trị chất xéa", "ngayBatDauDTCTX", dateEditor, { formatter: formatDate }),
                createEditableColumn("Dự phòng", "duPhong", "input"),
            ]);
            break;
    }

    // Cột thao tác (cột cuối cùng)
    columns.push({
        title: "Thao tác",
        formatter: function(cell, formatterParams, onRendered) {
            var row = cell.getRow();
            var data = row.getData();

            // Lấy row index trực tiếp từ Tabulator
            var uniqueRowIdentifier = row.getIndex();

            var tableId = cell.getTable().element.id;
            var isModalTable = tableId.startsWith('modal_');
            var xmlTypeForButton;

            if (isModalTable) {
                // Ví dụ: từ 'modal_xml2-edit-table' -> 'XML2'
                xmlTypeForButton = tableId.replace('modal_', '').replace('-edit-table', '').toUpperCase();
            } else {
                // Ví dụ: từ 'xml0-table' -> 'XML0'
                xmlTypeForButton = tableId.replace('-table', '').toUpperCase();
            }
            var maLK = data.maLK || data.ma_lk || ''; // Đảm bảo maLK có sẵn cho nút sửa
            var ngayTao = data.ngayTao || data.ngay_tao || ''; // Đảm bảo ngayTao có sẵn cho nút sửa

            var buttonsHtml = '';

            if (isModalTable) {
                // Nút xóa cho bảng trong modal
                buttonsHtml += `<button type="button" class="btn btn-sm btn-danger delete-modal-xml-item" data-id="${uniqueRowIdentifier}" data-type="${xmlTypeForButton}" title="Xóa dòng này khỏi modal"><i class="fas fa-trash"></i></button>`;
                // Bạn có thể thêm các nút khác cho modal ở đây nếu cần
            } else {
                // Nút cho bảng chính - phân biệt dữ liệu từ XML hay CSDL

                // Kiểm tra nguồn dữ liệu
                var isFromXML = window.isDataFromXML || false;

                if (isFromXML) {
                    // Dữ liệu từ XML upload - chỉ chỉnh sửa preview, không gọi server
                    buttonsHtml += `<button type="button" class="btn btn-sm btn-info edit-xml-preview-btn" data-row-index="${uniqueRowIdentifier}" data-type="${xmlTypeForButton}" title="Chỉnh sửa xem trước"><i class="fas fa-pencil-alt"></i></button>`;

                    // Nút xóa preview cho XML0 và XML1
                    if (xmlTypeForButton === 'XML0' || xmlTypeForButton === 'XML1') {
                        buttonsHtml += ` <button type="button" class="btn btn-sm btn-danger delete-xml-preview" data-row-index="${uniqueRowIdentifier}" data-type="${xmlTypeForButton}" title="Xóa khỏi xem trước"><i class="fas fa-trash"></i></button>`;
                    }
                } else {
                    // Dữ liệu từ CSDL - gọi server với maLK
                    buttonsHtml += `<button type="button" class="btn btn-sm btn-info edit-xml-all-btn" data-ma-lk="${maLK}" data-ngay-tao="${ngayTao}" title="Sửa hồ sơ từ CSDL"><i class="fas fa-pencil-alt"></i></button>`;

                    // Nút Save cho dữ liệu từ CSDL - chỉ hiển thị khi có thay đổi
                    buttonsHtml += ` <button type="button" class="btn btn-sm btn-success save-row-btn" data-id="${data.id || ''}" data-row-index="${uniqueRowIdentifier}" data-type="${xmlTypeForButton}" title="Lưu thay đổi" style="display:none;"><i class="fas fa-save"></i></button>`;

                    // Nút xóa từ CSDL cho XML1 (sử dụng database ID)
                    if (xmlTypeForButton === 'XML1') {
                        buttonsHtml += ` <button type="button" class="btn btn-sm btn-danger delete-xml" data-id="${data.id || ''}" data-row-index="${uniqueRowIdentifier}" data-ma-lk="${maLK}" data-type="${xmlTypeForButton}"  data-ngay-tao="${ngayTao}" title="Xóa từ CSDL"><i class="fas fa-trash"></i></button>`;
                    }
                    // Nút xóa từ CSDL cho XML khác (sử dụng database ID)
                    else if (xmlTypeForButton !== 'XML0') {
                        buttonsHtml += ` <button type="button" class="btn btn-sm btn-danger delete-xml" data-id="${data.id || ''}" data-row-index="${uniqueRowIdentifier}" data-ma-lk="${maLK}" data-type="${xmlTypeForButton}"  data-ngay-tao="${ngayTao}" title="Xóa từ CSDL"><i class="fas fa-trash"></i></button>`;
                    }
                }
            }
            return `<div class="btn-group">${buttonsHtml}</div>`;
        },
        width: 130,
        headerSort: false,
        hozAlign: "center",
        headerHozAlign: "center",
        headerVertical: false,
        resizable: false,
        cssClass: "action-column",
        frozen:true,
    });

    return columns;
}

// Hàm thiết lập Tabulator - CHỈ KHỞI TẠO, KHÔNG LOAD DỮ LIỆU
async function setupTables(xmlTypeToSetup, forceReinitialize = false) {
    if (!xmlTypeToSetup) {
        console.warn("setupTables: xmlTypeToSetup is required");
        return;
    }

    var tabId = xmlTypeToSetup.toLowerCase();
    var tableElementId = tabId + '-table';
    var tableElement = document.getElementById(tableElementId);

    if (!tableElement) {
        console.warn("setupTables: No table element found with ID: " + tableElementId);
        return;
    }

    // Kiểm tra DOM element có hợp lệ không
    if (!tableElement.parentNode || !document.contains(tableElement)) {
        console.error("setupTables: Table element is not in DOM for " + tableElementId);
        return;
    }

    // THAY ĐỔI: Luôn khởi tạo với local mode để tránh API call không cần thiết
    var initialPaginationMode = "local";
    var needsReinitialize = forceReinitialize;
    // Kiểm tra xem có cần reinitialize không
    if (tableElement._tabulator) {
        try {
            var existingMode = tableElement._tabulator.options ? tableElement._tabulator.options.paginationMode : null;
            // Chỉ reinitialize nếu được yêu cầu force
            if (forceReinitialize) {
                needsReinitialize = true;
            }
        } catch (error) {
            console.error(`setupTables: ${xmlTypeToSetup} - Error checking existing mode:`, error);
            needsReinitialize = true;
        }
    }

    // Destroy existing instance nếu cần reinitialize
    if (needsReinitialize && tableElement._tabulator) {
        try {
            if (tableElement._tabulator && typeof tableElement._tabulator.destroy === 'function') {
                tableElement._tabulator.destroy();
            }
            
            tableElement._tabulator = null;
            delete tableElement._tabulator;
            
            // Đợi cleanup hoàn tất
            await new Promise(resolve => setTimeout(resolve, 200));
        } catch (error) {
            console.error(`setupTables: ${xmlTypeToSetup} - Error destroying instance:`, error);
            tableElement._tabulator = null;
            delete tableElement._tabulator;
        }
    }

    // Khởi tạo Tabulator nếu chưa có hoặc đã được destroy
    if (!tableElement._tabulator) {
        try {
            var columns = getColumnsConfig(xmlTypeToSetup);
            if (!columns || columns.length === 0) {
                console.error(`setupTables: ${xmlTypeToSetup} - No columns configuration found`);
                return;
            }

            // Cấu hình Tabulator - THAY ĐỔI: Khởi tạo với local mode và empty data
            var tabulatorConfig = {
                columns: columns,
                layout: "fitDataFill",
                pagination: true,
                paginationMode: initialPaginationMode, // Luôn bắt đầu với local
                data: [], // THAY ĐỔI: Khởi tạo với data rỗng
                ajaxURL: "/xml4750/api/xml-data/", // Vẫn giữ để dùng khi chuyển sang remote
                paginationSize: 20,
                paginationSizeSelector: [10, 20, 50, 100],
                index: "maLK",
                editable: true,
                scrollX: true,
                autoResize: true,
                movableColumns: true,
                resizableRows: true,
                selectable: true,
                height: "calc(100vh - 400px)",
                placeholder: "Chưa có dữ liệu. Nhấn 'Tìm kiếm' để tải dữ liệu từ CSDL hoặc 'Import XML' để tải từ file.",
                tooltips: true,
                headerFilterLiveFilterDelay: 300,
                resizableColumnFit: true,
                fixedHeader: true,
                columnHeaderVertAlign: "middle",
                footerElement: `<div class="tabulator-footer-summary" style="padding: 5px 10px; border-top: 1px solid #dee2e6; background-color: #f8f9fa;">Đang tải tóm tắt...</div>`,

                // Event handlers
                tableBuilt: function() {
                    console.log(`setupTables: ${xmlTypeToSetup} - Table built successfully`);
                },

                dataLoaded: function(data) {
                    try {
                        setTimeout(() => {
                            if (xmlTypeToSetup === 'XML1' && typeof window.updateXML1Summary === 'function') {
                                window.updateXML1Summary();
                            } else if (xmlTypeToSetup === 'XML2' && typeof window.updateXML2Summary === 'function') {
                                window.updateXML2Summary();
                            } else if (xmlTypeToSetup === 'XML3' && typeof window.updateXML3Summary === 'function') {
                                window.updateXML3Summary();
                            }
                        }, 100);
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in dataLoaded handler:`, error);
                    }
                },

                dataLoadError: function(error) {
                    console.error(`setupTables: ${xmlTypeToSetup} - Data load error:`, error);
                    if (typeof showNotification === 'function') {
                        showNotification(`Lỗi tải dữ liệu ${xmlTypeToSetup}: ` + error, 'error');
                    }
                },

                dataFiltered: function(filters, rows) {
                    try {
                        if (xmlTypeToSetup === 'XML1' && typeof window.debouncedUpdateXML1Summary === 'function') {
                            window.debouncedUpdateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' && typeof window.debouncedUpdateXML2Summary === 'function') {
                            window.debouncedUpdateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' && typeof window.debouncedUpdateXML3Summary === 'function') {
                            window.debouncedUpdateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in dataFiltered handler:`, error);
                    }
                },

                dataSorted: function(sorters, rows) {
                    try {
                        if (xmlTypeToSetup === 'XML1' && typeof window.debouncedUpdateXML1Summary === 'function') {
                            window.debouncedUpdateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' && typeof window.debouncedUpdateXML2Summary === 'function') {
                            window.debouncedUpdateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' && typeof window.debouncedUpdateXML3Summary === 'function') {
                            window.debouncedUpdateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in dataSorted handler:`, error);
                    }
                },

                pageLoaded: function(pageno) {
                    try {
                        console.log(`setupTables: ${xmlTypeToSetup} - Page ${pageno} loaded`);
                        setTimeout(() => {
                            if (xmlTypeToSetup === 'XML1' && typeof window.updateXML1Summary === 'function') {
                                window.updateXML1Summary();
                            } else if (xmlTypeToSetup === 'XML2' && typeof window.updateXML2Summary === 'function') {
                                window.updateXML2Summary();
                            } else if (xmlTypeToSetup === 'XML3' && typeof window.updateXML3Summary === 'function') {
                                window.updateXML3Summary();
                            }
                        }, 100);
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in pageLoaded handler:`, error);
                    }
                },

                dataChanged: function(data) {
                    try {
                        if (xmlTypeToSetup === 'XML1' && typeof window.debouncedUpdateXML1Summary === 'function') {
                            window.debouncedUpdateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' && typeof window.debouncedUpdateXML2Summary === 'function') {
                            window.debouncedUpdateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' && typeof window.debouncedUpdateXML3Summary === 'function') {
                            window.debouncedUpdateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in dataChanged handler:`, error);
                    }
                },

                rowAdded: function(row) {
                    try {
                        if (xmlTypeToSetup === 'XML1' && typeof window.debouncedUpdateXML1Summary === 'function') {
                            window.debouncedUpdateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' && typeof window.debouncedUpdateXML2Summary === 'function') {
                            window.debouncedUpdateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' && typeof window.debouncedUpdateXML3Summary === 'function') {
                            window.debouncedUpdateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in rowAdded handler:`, error);
                    }
                },

                rowDeleted: function(row) {
                    try {
                        if (xmlTypeToSetup === 'XML1' && typeof window.debouncedUpdateXML1Summary === 'function') {
                            window.debouncedUpdateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' && typeof window.debouncedUpdateXML2Summary === 'function') {
                            window.debouncedUpdateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' && typeof window.debouncedUpdateXML3Summary === 'function') {
                            window.debouncedUpdateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in rowDeleted handler:`, error);
                    }
                },

                cellEdited: function(cell) {
                    try {
                        // Logic hiển thị nút Save đã được chuyển sang tabulator_xml_func.js
                        // để tránh trùng lặp và đảm bảo consistency

                        // Cập nhật summary cho các trường tài chính
                        if (xmlTypeToSetup === 'XML1' &&
                            ['tTongChiBV', 'tTongChiBH', 'tBNTT', 'tBNCCT', 'tBHTT'].includes(cell.getField()) &&
                            typeof window.updateXML1Summary === 'function') {
                            window.updateXML1Summary();
                        } else if (xmlTypeToSetup === 'XML2' &&
                            ['thanhTienBV', 'thanhTienBH', 'tBNTT', 'tBNCCT', 'tBHTT'].includes(cell.getField()) &&
                            typeof window.updateXML2Summary === 'function') {
                            window.updateXML2Summary();
                        } else if (xmlTypeToSetup === 'XML3' &&
                            ['thanhTienBV', 'thanhTienBH', 'tBNTT', 'tBNCCT', 'tBHTT'].includes(cell.getField()) &&
                            typeof window.updateXML3Summary === 'function') {
                            window.updateXML3Summary();
                        }
                    } catch (error) {
                        console.error(`setupTables: ${xmlTypeToSetup} - Error in cellEdited handler:`, error);
                    }
                },
                
                // Language configuration
                langs: {
                    "vi-vn": {
                        "columns": {
                            "name": "Tên",
                            "progress": "Tiến độ",
                            "gender": "Giới tính",
                            "rating": "Đánh giá",
                            "col": "Cột"
                        },
                        "ajax": {
                            "loading": "Đang tải",
                            "error": "Lỗi"
                        },
                        "pagination": {
                            "page_size": "Kích thước trang",
                            "page_title": "Hiển thị trang",
                            "first": "Đầu",
                            "first_title": "Trang đầu",
                            "last": "Cuối",
                            "last_title": "Trang cuối",
                            "prev": "Trước",
                            "prev_title": "Trang trước",
                            "next": "Tiếp",
                            "next_title": "Trang tiếp theo"
                        },
                        "headerFilters": {
                            "default": "Tìm kiếm...",
                        }
                    }
                }
            };

            // Khởi tạo Tabulator với error handling
            try {
                tableElement._tabulator = new Tabulator(tableElement, tabulatorConfig);
                
                // Đợi Tabulator khởi tạo hoàn tất
                await new Promise(resolve => setTimeout(resolve, 300));
                
                // Kiểm tra khởi tạo thành công
                if (!tableElement._tabulator) {
                    throw new Error(`Tabulator initialization for ${tabId} returned null/undefined`);
                }
                setupPaginationEventHandlers(tableElement._tabulator, xmlTypeToSetup);
            } catch (initError) {
                console.error(`setupTables: Critical error during Tabulator initialization for ${tabId}:`, initError);
                
                // Cleanup on failed initialization
                if (tableElement._tabulator) {
                    try {
                        tableElement._tabulator.destroy();
                    } catch (destroyError) {
                        console.error(`setupTables: Error during cleanup after failed init:`, destroyError);
                    }
                    tableElement._tabulator = null;
                    delete tableElement._tabulator;
                }
                
                if (typeof showNotification === 'function') {
                    showNotification(`Lỗi khởi tạo bảng ${xmlTypeToSetup}. Vui lòng tải lại trang.`, 'error');
                }
                
                return;
            }
            
        } catch (error) {
            console.error(`setupTables: Error during setup for ${tabId}:`, error);
            
            if (typeof showNotification === 'function') {
                showNotification(`Lỗi thiết lập bảng ${xmlTypeToSetup}. Vui lòng thử lại.`, 'error');
            }
        }
    } else {
        console.log(`setupTables: ${xmlTypeToSetup} - Tabulator instance already exists`);
    }
}

async function loadTableData(xmlType, filters = {}, useLocalMode = true) {
    try {
        console.log(`loadTableData: Loading ${xmlType} in ${useLocalMode ? 'LOCAL' : 'REMOTE'} mode...`);
        console.log(`loadTableData: ${xmlType} - Filters:`, filters);
        
        // Get tabulator instance
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (!tableElement || !tableElement._tabulator) {
            console.warn(`loadTableData: ${xmlType} - Table element not found or not initialized`);
            return { success: false, error: 'tabulator_not_found', dataCount: 0 };
        }
        
        const tabulator = tableElement._tabulator;
        
        if (useLocalMode) {
            // LOCAL MODE: Data from uploaded XML files - already parsed and available
            console.log(`loadTableData: ${xmlType} - Using LOCAL mode (data from uploaded XML)...`);
            
            // Ensure local pagination mode
            tabulator.options.paginationMode = 'local';
            
            // Clear any AJAX configuration
            tabulator.options.ajaxURL = null;
            tabulator.options.ajaxParams = null;
            tabulator.options.ajaxParamsFunc = null;
            tabulator.options.ajaxResponse = null;
            tabulator.options.ajaxError = null;
            
            // In local mode, data should already be available from XML parsing
            // We don't need to fetch from server - data is passed directly via loadDataIntoTable()
            console.log(`loadTableData: ${xmlType} - LOCAL mode configured, waiting for data to be set via loadDataIntoTable()`);
            
            const currentCount = tabulator.getDataCount();
            console.log(`loadTableData: ${xmlType} - Current data count: ${currentCount}`);
            
            return { success: true, dataCount: currentCount };
            
        } else {
            // REMOTE MODE: Server-side pagination for database data
            console.log(`loadTableData: ${xmlType} - Using REMOTE mode (server-side pagination)...`);
            
            // Ensure remote pagination mode
            tabulator.options.paginationMode = 'remote';
            
            // Set base AJAX URL (without parameters)
            const baseURL = '/xml4750/api/xml-data/';
            tabulator.options.ajaxURL = baseURL;
            
            console.log(`loadTableData: ${xmlType} - Base AJAX URL: ${baseURL}`);
            
            // Configure AJAX config
            tabulator.options.ajaxConfig = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            // Build base parameters that will always be sent
            const baseParams = {
                xml_type: xmlType
            };
            
            // Add filters to base parameters
            Object.keys(filters).forEach(key => {
                if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
                    baseParams[key] = filters[key];
                }
            });
            
            console.log(`loadTableData: ${xmlType} - Base params:`, baseParams);
            
            // Set base AJAX parameters
            tabulator.options.ajaxParams = baseParams;
            
            // Enhanced AJAX parameters function với error handling và sorting cải thiện
            tabulator.options.ajaxParamsFunc = function(url, config, params) {
                try {
                    console.log(`loadTableData: ${xmlType} - ajaxParamsFunc called with:`, {
                        url: url,
                        config: config,
                        params: params
                    });

                    // Start with base parameters
                    const ajaxParams = { ...baseParams };

                    // Add pagination parameters from Tabulator
                    ajaxParams.page = params.page || 1;
                    ajaxParams.size = params.size || 20;

                    // Enhanced sorting support - sử dụng format mà API mong đợi
                    if (params.sort && params.sort.length > 0) {
                        const sortInfo = params.sort[0]; // Lấy sort đầu tiên
                        ajaxParams.sort = sortInfo.field;
                        ajaxParams.dir = sortInfo.dir || 'asc';
                        console.log(`loadTableData: ${xmlType} - Applied sorting: ${sortInfo.field} ${sortInfo.dir}`);
                    }

                    // Add search parameter if present
                    if (params.search) {
                        ajaxParams.search = params.search;
                    }

                    // Add any additional filters from Tabulator header filters
                    if (params.filter && params.filter.length > 0) {
                        params.filter.forEach(filter => {
                            if (filter.value !== null && filter.value !== undefined && filter.value !== '') {
                                ajaxParams[`filter_${filter.field}`] = filter.value;
                            }
                        });
                    }

                    console.log(`loadTableData: ${xmlType} - Final AJAX params:`, ajaxParams);
                    return ajaxParams;

                } catch (error) {
                    console.error(`loadTableData: ${xmlType} - ajaxParamsFunc error:`, error);
                    // Return minimal params on error
                    return {
                        xml_type: xmlType,
                        page: params.page || 1,
                        size: params.size || 20
                    };
                }
            };
            
            // Alternative approach: Build URL with parameters directly
            const alternativeApproach = false; // Set to true to use URL building approach
            
            if (alternativeApproach) {
                // Build URL with base parameters
                const urlParams = new URLSearchParams(baseParams);
                const fullURL = `${baseURL}?${urlParams.toString()}`;
                tabulator.options.ajaxURL = fullURL;
                console.log(`loadTableData: ${xmlType} - Full URL with params: ${fullURL}`);
                
                // Clear ajaxParams and ajaxParamsFunc when using URL approach
                tabulator.options.ajaxParams = null;
                tabulator.options.ajaxParamsFunc = null;
            }
            
            // Configure AJAX response handler for pagination
            tabulator.options.ajaxResponse = function(url, params, response) {
                try {
                    console.log(`loadTableData: ${xmlType} - ajaxResponse called with response:`, response);
                    console.log(`loadTableData: ${xmlType} - ajaxResponse params:`, params);
                    
                    // Enhanced response validation
                    if (!response) {
                        console.error(`loadTableData: ${xmlType} - No response received`);
                        return { data: [], last_page: 1 };
                    }

                    // Handle API error responses
                    if (response.error) {
                        console.error(`loadTableData: ${xmlType} - API error:`, response.error);
                        if (typeof showNotification === 'function') {
                            showNotification(`Lỗi tải dữ liệu ${xmlType}: ${response.error}`, 'error');
                        }
                        return { data: [], last_page: 1 };
                    }
                    
                    // Extract data array
                    let responseData = [];
                    let paginationInfo = null;
                    
                    if (Array.isArray(response)) {
                        // Direct array response
                        console.log(`loadTableData: ${xmlType} - Response is direct array, count: ${response.length}`);
                        responseData = response;
                    } else if (response && typeof response === 'object') {
                        if (Array.isArray(response.data)) {
                            console.log(`loadTableData: ${xmlType} - Response has data array, count: ${response.data.length}`);
                            responseData = response.data;
                            
                            // Store pagination info for manual handling
                            if (response.pagination) {
                                console.log(`loadTableData: ${xmlType} - Found pagination info:`, response.pagination);
                                paginationInfo = {
                                    total: response.pagination.total || response.pagination.total_count || 0,
                                    last_page: response.pagination.last_page || response.pagination.total_pages || 1,
                                    current_page: response.pagination.current_page || params.page || 1,
                                    per_page: response.pagination.per_page || params.size || 50,
                                    has_next: response.pagination.has_next || false,
                                    has_prev: response.pagination.has_prev || false
                                };
                                
                                console.log(`loadTableData: ${xmlType} - Extracted pagination info:`, paginationInfo);
                                
                                // Store pagination info globally for later use
                                window.tabulatorPaginationInfo = window.tabulatorPaginationInfo || {};
                                window.tabulatorPaginationInfo[xmlType] = paginationInfo;
                                
                                // Set pagination info with proper timing
                                setTimeout(() => {
                                    setPaginationInfo(tabulator, xmlType, paginationInfo);
                                }, 100);
                            }
                        } else if (response.data === null || response.data === undefined) {
                            console.log(`loadTableData: ${xmlType} - Response data is null/undefined, returning empty`);
                            responseData = [];
                        } else {
                            console.warn(`loadTableData: ${xmlType} - Response has no valid data array:`, response);
                            responseData = [];
                        }
                    } else {
                        console.warn(`loadTableData: ${xmlType} - Unexpected response format:`, typeof response, response);
                        responseData = [];
                    }
                    
                    console.log(`loadTableData: ${xmlType} - Returning data array with ${responseData.length} items`);
                    console.log(`loadTableData: ${xmlType} - Sample data:`, responseData.slice(0, 2));

                    // Enhanced return với pagination support
                    if (paginationInfo) {
                        console.log(`loadTableData: ${xmlType} - Returning with pagination info`);
                        // Store pagination info for later use
                        window.tabulatorPaginationInfo = window.tabulatorPaginationInfo || {};
                        window.tabulatorPaginationInfo[xmlType] = paginationInfo;

                        // Return object format that Tabulator can understand
                        return {
                            data: responseData,
                            last_page: paginationInfo.last_page,
                            total_count: paginationInfo.total
                        };
                    } else {
                        // Return array directly for simple cases
                        return responseData;
                    }

                } catch (error) {
                    console.error(`loadTableData: ${xmlType} - ajaxResponse error:`, error);
                    if (typeof showNotification === 'function') {
                        showNotification(`Lỗi xử lý dữ liệu ${xmlType}: ${error.message}`, 'error');
                    }
                    return { data: [], last_page: 1 };
                }
            };

            
            // Configure AJAX error handler
            tabulator.options.ajaxError = function(xhr, textStatus, errorThrown) {
                console.error(`loadTableData: ${xmlType} - AJAX Error:`, {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    textStatus: textStatus,
                    errorThrown: errorThrown,
                    responseText: xhr.responseText,
                    url: xhr.responseURL,
                    requestURL: xhr.responseURL || 'Unknown'
                });
                
                let errorMessage = `Lỗi tải dữ liệu ${xmlType}`;

                // Provide more specific error messages based on status
                if (xhr.status === 0) {
                    errorMessage += ': Không thể kết nối đến server';
                } else if (xhr.status === 404) {
                    errorMessage += ': API endpoint không tồn tại';
                } else if (xhr.status === 500) {
                    errorMessage += ': Lỗi server nội bộ';
                } else if (xhr.status === 403) {
                    errorMessage += ': Không có quyền truy cập';
                } else if (textStatus === 'timeout') {
                    errorMessage += ': Server phản hồi quá chậm';
                } else {
                    try {
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage += ": " + xhr.responseJSON.message;
                        } else if (xhr.responseText) {
                            const errorResponse = JSON.parse(xhr.responseText);
                            if (errorResponse.message) {
                                errorMessage += ": " + errorResponse.message;
                            } else {
                                errorMessage += ": " + textStatus;
                            }
                        } else {
                            errorMessage += ": " + textStatus;
                        }
                    } catch (e) {
                        errorMessage += ": " + textStatus;
                    }
                }

                // Log the actual request that was made
                console.error(`loadTableData: ${xmlType} - Request details:`, {
                    method: xhr.type || 'GET',
                    url: xhr.responseURL,
                    status: xhr.status,
                    statusText: xhr.statusText
                });

                // Show user-friendly notification
                if (typeof showNotification === 'function') {
                    showNotification(errorMessage, 'error');
                }

                // Return empty data structure for Tabulator
                return { data: [], last_page: 1 };
            };
            
            // Test AJAX configuration before triggering load
            console.log(`loadTableData: ${xmlType} - AJAX Configuration:`, {
                ajaxURL: tabulator.options.ajaxURL,
                ajaxParams: tabulator.options.ajaxParams,
                hasAjaxParamsFunc: typeof tabulator.options.ajaxParamsFunc === 'function',
                paginationMode: tabulator.options.paginationMode
            });
            
            // Trigger AJAX load (this will automatically handle pagination)
            console.log(`loadTableData: ${xmlType} - Triggering AJAX load for remote pagination...`);
            
            try {
                // Force first page load with explicit parameters
                await tabulator.setData();
                
                // Get current page count after loading
                const currentPageCount = tabulator.getDataCount();
                console.log(`loadTableData: ${xmlType} - Remote mode configured successfully, current page count: ${currentPageCount}`);
                
                return { success: true, dataCount: currentPageCount };
                
            } catch (ajaxError) {
                console.error(`loadTableData: ${xmlType} - AJAX load failed:`, ajaxError);
                
                // Try alternative approach if first attempt fails
                if (!alternativeApproach) {
                    console.log(`loadTableData: ${xmlType} - Trying alternative URL approach...`);
                    
                    try {
                        // Build URL with parameters directly
                        const urlParams = new URLSearchParams(baseParams);
                        urlParams.append('page', '1');
                        urlParams.append('size', '50');
                        const fullURL = `${baseURL}?${urlParams.toString()}`;
                        
                        console.log(`loadTableData: ${xmlType} - Alternative URL: ${fullURL}`);
                        
                        tabulator.options.ajaxURL = fullURL;
                        tabulator.options.ajaxParams = null;
                        tabulator.options.ajaxParamsFunc = null;
                        
                        await tabulator.setData();
                        
                        const currentPageCount = tabulator.getDataCount();
                        console.log(`loadTableData: ${xmlType} - Alternative approach successful, current page count: ${currentPageCount}`);
                        
                        return { success: true, dataCount: currentPageCount };
                        
                    } catch (alternativeError) {
                        console.error(`loadTableData: ${xmlType} - Alternative approach also failed:`, alternativeError);
                        return { success: false, error: alternativeError.message || 'Both AJAX approaches failed', dataCount: 0 };
                    }
                } else {
                    return { success: false, error: ajaxError.message || 'AJAX load failed', dataCount: 0 };
                }
            }
        }
        
    } catch (error) {
        console.error(`loadTableData: ${xmlType} - Error:`, error);
        return { success: false, error: error.message, dataCount: 0 };
    }
}

// Enhanced loadDataIntoTable - used for LOCAL mode (uploaded XML data)
async function loadDataIntoTable(xmlType, data) {
    try {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (!tableElement || !tableElement._tabulator) {
            console.warn(`loadDataIntoTable: ${xmlType} - Table not found or not initialized`);
            return false;
        }

        const tabulator = tableElement._tabulator;
        
        // Force local mode for uploaded data
        tabulator.options.paginationMode = 'local';
        
        // Clear AJAX configuration (important for local mode)
        tabulator.options.ajaxURL = null;
        tabulator.options.ajaxParams = null;
        tabulator.options.ajaxParamsFunc = null;
        tabulator.options.ajaxResponse = null;
        tabulator.options.ajaxError = null;
        
        // Set data directly - this is for uploaded XML data
        const dataToSet = Array.isArray(data) ? data : [];
        await tabulator.setData(dataToSet);
        
        const finalCount = tabulator.getDataCount();
        return true;
        
    } catch (error) {
        console.error(`loadDataIntoTable: Error loading ${xmlType}:`, error);
        return false;
    }
}

// Enhanced loadAllXMLData function with correct mode handling
async function loadAllXMLData(filters = {}) {   
    try {
        // Step 1: Update global filters
        window.currentGlobalFilters = { ...window.currentGlobalFilters, ...filters };

        // Step 2: Determine mode based on data source
        const useLocalMode = window.isDataFromUpload;

        if (useLocalMode) {
            // In local mode, data is already loaded via XML parsing and loadDataIntoTable()
            // We just need to ensure pagination mode is set correctly
            await switchPaginationMode(true);
            
            return { successCount: 16, errorCount: 0, totalCount: 16 };
        } else {
            // Step 3: Switch pagination mode for all tables
            await switchPaginationMode(false);
            
            // Step 4: Load data for each table from server
            const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
            
            let successCount = 0;
            let errorCount = 0;
            
            for (let i = 0; i < xmlTypes.length; i++) {
                const xmlType = xmlTypes[i];
                try {
                    const result = await loadTableData(xmlType, window.currentGlobalFilters, false);
                    
                    if (result.success) {
                        successCount++;
                    } else {
                        errorCount++;
                        console.warn(`loadAllXMLData: ${xmlType} - FAILED:`, result.error);
                    }
                    
                    // Short delay between tables to prevent overwhelming server
                    if (i < xmlTypes.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }
                    
                } catch (error) {
                    errorCount++;
                    console.error(`loadAllXMLData: ${xmlType} - Error:`, error);
                }
            }
            // Show notification
            if (successCount > 0) {
                console.log(`loadAllXMLData: Successfully loaded ${successCount} tables from server`);
            } else {
                console.warn('loadAllXMLData: No tables loaded successfully from server');
            }
            
            return { successCount, errorCount, totalCount: xmlTypes.length };
        }
        
    } catch (error) {
        console.error("loadAllXMLData: Critical error:", error);
        throw error;
    }
}

// Enhanced switchPaginationMode function
async function switchPaginationMode(useLocalMode = true) {
    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    const targetMode = useLocalMode ? 'local' : 'remote';
    
    console.log(`switchPaginationMode: Switching to ${targetMode} mode`);
    
    let changeCount = 0;
    
    for (const xmlType of xmlTypes) {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (tableElement && tableElement._tabulator) {
            const tabulator = tableElement._tabulator;
            const currentMode = tabulator.options?.paginationMode;
            
            if (currentMode !== targetMode) {
                console.log(`switchPaginationMode: ${xmlType} changing from ${currentMode} to ${targetMode}`);
                
                tabulator.options.paginationMode = targetMode;
                
                // Clear AJAX configuration when switching to local mode
                if (useLocalMode) {
                    tabulator.options.ajaxURL = null;
                    tabulator.options.ajaxParams = null;
                    tabulator.options.ajaxParamsFunc = null;
                    tabulator.options.ajaxResponse = null;
                    tabulator.options.ajaxError = null;
                    console.log(`switchPaginationMode: ${xmlType} - Cleared AJAX config for local mode`);
                }
                
                changeCount++;
            } else {
                console.log(`switchPaginationMode: ${xmlType} already in ${targetMode} mode`);
            }
        } else {
            console.warn(`switchPaginationMode: ${xmlType} - Table not found or not initialized`);
        }
    }
    
    console.log(`switchPaginationMode: Changed ${changeCount} tables to ${targetMode} mode`);
    
    // Wait for changes to be applied
    if (changeCount > 0) {
        await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    return changeCount;
}

// Helper function to refresh a single table
async function refreshTable(xmlType) {
    try {
        console.log(`refreshTable: Refreshing ${xmlType}...`);
        
        const useLocalMode = window.isDataFromUpload;
        
        if (useLocalMode) {
            // In local mode, just redraw the table as data is already loaded
            console.log(`refreshTable: ${xmlType} - LOCAL mode, just redrawing table`);
            const tableElementId = xmlType.toLowerCase() + '-table';
            const tableElement = document.getElementById(tableElementId);
            if (tableElement && tableElement._tabulator) {
                tableElement._tabulator.redraw(true);
                return true;
            }
            return false;
        } else {
            // In remote mode, reload data from server
            console.log(`refreshTable: ${xmlType} - REMOTE mode, reloading from server`);
            const result = await loadTableData(xmlType, window.currentGlobalFilters || {}, false);
            
            if (result.success) {
                console.log(`refreshTable: ${xmlType} refreshed successfully`);
                
                // Redraw the table to ensure proper display
                const tableElementId = xmlType.toLowerCase() + '-table';
                const tableElement = document.getElementById(tableElementId);
                if (tableElement && tableElement._tabulator) {
                    tableElement._tabulator.redraw(true);
                }
                
                return true;
            } else {
                console.error(`refreshTable: ${xmlType} failed:`, result.error);
                return false;
            }
        }
    } catch (error) {
        console.error(`refreshTable: ${xmlType} error:`, error);
        return false;
    }
}

// Additional helper functions
async function getTableData(xmlType) {
    try {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (!tableElement || !tableElement._tabulator) {
            console.warn(`getTableData: ${xmlType} - Table not found`);
            return [];
        }
        
        return tableElement._tabulator.getData();
    } catch (error) {
        console.error(`getTableData: ${xmlType} - Error:`, error);
        return [];
    }
}

async function clearTableData(xmlType) {
    try {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (!tableElement || !tableElement._tabulator) {
            console.warn(`clearTableData: ${xmlType} - Table not found`);
            return false;
        }

        await tableElement._tabulator.clearData();
        console.log(`clearTableData: ${xmlType} - Data cleared`);
        return true;
    } catch (error) {
        console.error(`clearTableData: ${xmlType} - Error:`, error);
        return false;
    }
}

async function deleteTableRow(xmlType, rowId, maLK, ngayTao, rowPosition) {
    try {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);

        if (!tableElement || !tableElement._tabulator) {
            console.warn(`deleteTableRow: ${xmlType} - Table not found`);
            return false;
        }

        // Tìm row theo nhiều cách
        let row = null;

        // 1. Thử tìm theo array index trước
        if (rowPosition !== undefined && rowPosition !== null && !isNaN(rowPosition) && rowPosition >= 0) {
            try {
                const allRows = tableElement._tabulator.getRows();
                if (allRows[rowPosition]) {
                    row = allRows[rowPosition];
                    console.log(`deleteTableRow: Found row by array index ${rowPosition}`);
                }
            } catch (error) {
                console.warn('Could not find row by array index:', error);
            }
        }

        // 2. Nếu không tìm được, thử theo ID
        if (!row && rowId) {
            try {
                row = tableElement._tabulator.getRow(rowId);
                if (row) {
                    console.log(`deleteTableRow: Found row by ID ${rowId}`);
                }
            } catch (error) {
                console.warn('Could not find row by ID:', error);
            }
        }

        // 3. Nếu vẫn không tìm được, thử theo maLK
        if (!row && maLK) {
            try {
                const allRows = tableElement._tabulator.getRows();
                row = allRows.find(r => {
                    const data = r.getData();
                    return (data.maLK === maLK || data.ma_lk === maLK);
                });
                if (row) {
                    console.log(`deleteTableRow: Found row by maLK ${maLK}`);
                }
            } catch (error) {
                console.warn('Could not find row by maLK:', error);
            }
        }

        if (!row) {
            console.warn(`deleteTableRow: ${xmlType} - No row found with position:${rowPosition}, ID:${rowId}, maLK:${maLK}`);
            return false;
        }

        // Check if data is from XML preview
        const isFromXML = window.isDataFromXML || window.currentDataSource === 'imported_xml';

        if (isFromXML && (xmlType === 'XML0' || xmlType === 'XML1')) {
            // For XML0 and XML1 in preview mode, delete all rows with the same maLK
            const rowMaLK = row.getData().maLK;
            if (rowMaLK) {
                console.log(`deleteTableRow: ${xmlType} - Deleting all rows with maLK: ${rowMaLK} (preview mode)`);

                // Delete from all tables that have this maLK
                let totalDeleted = 0;
                for (let i = 0; i <= 15; i++) {
                    const currentXmlType = `XML${i}`;
                    const currentTableElementId = currentXmlType.toLowerCase() + '-table';
                    const currentTableElement = document.getElementById(currentTableElementId);

                    if (currentTableElement && currentTableElement._tabulator) {
                        const rowsToDelete = currentTableElement._tabulator.getRows().filter(r => r.getData().maLK === rowMaLK);
                        for (const rowToDelete of rowsToDelete) {
                            await rowToDelete.delete();
                            totalDeleted++;
                        }
                        if (rowsToDelete.length > 0) {
                            console.log(`deleteTableRow: Deleted ${rowsToDelete.length} rows from ${currentXmlType}`);
                        }
                    }
                }

                console.log(`deleteTableRow: Total ${totalDeleted} rows deleted for maLK: ${rowMaLK}`);
                return totalDeleted > 0;
            } else {
                console.warn(`deleteTableRow: ${xmlType} - Row has no maLK`);
                return false;
            }
        } else {
            // Normal single row deletion
            await row.delete();
            console.log(`deleteTableRow: ${xmlType} - Row deleted successfully`);
            return true;
        }
    } catch (error) {
        console.error(`deleteTableRow: ${xmlType} - Error:`, error);
        return false;
    }
}

// Function to set pagination info with multiple approaches
function setPaginationInfo(tabulator, xmlType, paginationInfo) {
    try {
        console.log(`setPaginationInfo: ${xmlType} - Setting pagination:`, paginationInfo);
        
        // Method 1: Direct module access
        if (tabulator.modules && tabulator.modules.page) {
            const pageModule = tabulator.modules.page;
            console.log(`setPaginationInfo: ${xmlType} - Found page module`);
            
            try {
                // Set max page
                if (typeof pageModule.setMaxPage === 'function') {
                    pageModule.setMaxPage(paginationInfo.last_page);
                    console.log(`setPaginationInfo: ${xmlType} - Set max page to:`, paginationInfo.last_page);
                }
                
                // Set current page
                if (typeof pageModule.setPage === 'function') {
                    pageModule.setPage(paginationInfo.current_page);
                    console.log(`setPaginationInfo: ${xmlType} - Set current page to:`, paginationInfo.current_page);
                }
                
                // Set total count
                if (typeof pageModule.setTotalCount === 'function') {
                    pageModule.setTotalCount(paginationInfo.total);
                    console.log(`setPaginationInfo: ${xmlType} - Set total count to:`, paginationInfo.total);
                }
                
                // Update page size
                if (typeof pageModule.setPageSize === 'function') {
                    pageModule.setPageSize(paginationInfo.per_page);
                    console.log(`setPaginationInfo: ${xmlType} - Set page size to:`, paginationInfo.per_page);
                }
                
                // Force render pagination
                if (typeof pageModule.renderPagination === 'function') {
                    pageModule.renderPagination();
                    console.log(`setPaginationInfo: ${xmlType} - Forced pagination render`);
                }
                
            } catch (moduleError) {
                console.warn(`setPaginationInfo: ${xmlType} - Module method error:`, moduleError);
            }
        }
        
        // Method 2: Update options and trigger redraw
        try {
            // Update pagination options
            if (tabulator.options.paginationSize !== paginationInfo.per_page) {
                tabulator.options.paginationSize = paginationInfo.per_page;
                console.log(`setPaginationInfo: ${xmlType} - Updated paginationSize to:`, paginationInfo.per_page);
            }
            
            // Store pagination info in tabulator instance
            tabulator.paginationInfo = paginationInfo;
            
            // Trigger redraw
            if (typeof tabulator.redraw === 'function') {
                tabulator.redraw();
                console.log(`setPaginationInfo: ${xmlType} - Triggered redraw`);
            }
            
        } catch (optionsError) {
            console.warn(`setPaginationInfo: ${xmlType} - Options update error:`, optionsError);
        }
        
        // Method 3: Manual DOM manipulation as fallback
        setTimeout(() => {
            updatePaginationDOM(xmlType, paginationInfo);
        }, 200);
        
    } catch (error) {
        console.error(`setPaginationInfo: ${xmlType} - Error:`, error);
    }
}

// Function to manually update pagination DOM elements
function updatePaginationDOM(xmlType, paginationInfo) {
    try {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (!tableElement) {
            console.warn(`updatePaginationDOM: ${xmlType} - Table element not found`);
            return;
        }
        
        // Find pagination container
        const paginationContainer = tableElement.querySelector('.tabulator-paginator');
        if (!paginationContainer) {
            console.warn(`updatePaginationDOM: ${xmlType} - Pagination container not found`);
            return;
        }
        
        console.log(`updatePaginationDOM: ${xmlType} - Found pagination container`);
        
        // Update page info display
        const pageInfo = paginationContainer.querySelector('.tabulator-page-counter');
        if (pageInfo) {
            const startRecord = ((paginationInfo.current_page - 1) * paginationInfo.per_page) + 1;
            const endRecord = Math.min(paginationInfo.current_page * paginationInfo.per_page, paginationInfo.total);
            pageInfo.textContent = `Showing ${startRecord} to ${endRecord} of ${paginationInfo.total} entries`;
            console.log(`updatePaginationDOM: ${xmlType} - Updated page info`);
        }
        
        // Update page buttons
        const pageButtons = paginationContainer.querySelectorAll('.tabulator-page');
        pageButtons.forEach(button => {
            const pageNum = parseInt(button.getAttribute('data-page'));
            if (pageNum === paginationInfo.current_page) {
                button.classList.add('active');
            } else {
                button.classList.remove('active');
            }
        });
        
        // Update prev/next buttons
        const prevButton = paginationContainer.querySelector('.tabulator-page[data-page="prev"]');
        const nextButton = paginationContainer.querySelector('.tabulator-page[data-page="next"]');
        
        if (prevButton) {
            if (paginationInfo.current_page <= 1) {
                prevButton.classList.add('disabled');
            } else {
                prevButton.classList.remove('disabled');
            }
        }
        
        if (nextButton) {
            if (paginationInfo.current_page >= paginationInfo.last_page) {
                nextButton.classList.add('disabled');
            } else {
                nextButton.classList.remove('disabled');
            }
        }
        
        console.log(`updatePaginationDOM: ${xmlType} - Updated pagination DOM`);
        
    } catch (error) {
        console.error(`updatePaginationDOM: ${xmlType} - Error:`, error);
    }
}

// Enhanced function to handle pagination events
function setupPaginationEventHandlers(tabulator, xmlType) {
    try {
        // Listen for page change events
        tabulator.on("pageLoaded", function(pageno) {
            // Update stored pagination info
            if (window.tabulatorPaginationInfo && window.tabulatorPaginationInfo[xmlType]) {
                window.tabulatorPaginationInfo[xmlType].current_page = pageno;
            }
        });
        
        // Listen for data loaded events
        tabulator.on("dataLoaded", function(data) {
            // Apply stored pagination info if available
            if (window.tabulatorPaginationInfo && window.tabulatorPaginationInfo[xmlType]) {
                setTimeout(() => {
                    setPaginationInfo(tabulator, xmlType, window.tabulatorPaginationInfo[xmlType]);
                }, 100);
            }
        });
        
        // Listen for pagination events
        // tabulator.on("pagePrev", function(page) {
        //     console.log(`setupPaginationEventHandlers: ${xmlType} - Page prev:`, page);
        // });
        
        // tabulator.on("pageNext", function(page) {
        //     console.log(`setupPaginationEventHandlers: ${xmlType} - Page next:`, page);
        // });
        
    } catch (error) {
        console.error(`setupPaginationEventHandlers: ${xmlType} - Error:`, error);
    }
}

// Function to show loading state for all tables
function showLoadingForAllTables() {
    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    xmlTypes.forEach(xmlType => {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (tableElement && tableElement._tabulator) {
            try {
                // Show loading placeholder
                tableElement._tabulator.setData([]);
            } catch (error) {
                console.error(`showLoadingForAllTables: Error showing loading for ${xmlType}:`, error);
            }
        }
    });
}

// Hàm ẩn loading cho tất cả các bảng
function hideLoadingForAllTables() {
    // Hiện tại không cần làm gì đặc biệt
    // Loading sẽ tự động ẩn khi data được load
    console.log("hideLoadingForAllTables: Loading hidden for all tables");
}

// Hàm để reload dữ liệu cho tất cả các bảng
async function reloadAllTableData(filters = {}) {
    if (window.isDataFromUpload) {
        if (typeof showNotification === 'function') {
            showNotification('Chế độ local: Cần implement logic reload dữ liệu local', 'info');
        }
    } else {
        // Remote mode - gọi loadAllXMLData
        await loadAllXMLData(filters);
    }
}

// Hàm helper để kiểm tra trạng thái Tabulator
function checkTabulatorHealth(xmlType) {
    var tableElementId = xmlType.toLowerCase() + '-table';
    var tableElement = document.getElementById(tableElementId);
    
    if (!tableElement) {
        console.error(`checkTabulatorHealth: No DOM element found for ${xmlType}`);
        return false;
    }
    
    if (!tableElement._tabulator) {
        console.error(`checkTabulatorHealth: No Tabulator instance found for ${xmlType}`);
        return false;
    }
    
    try {
        // Kiểm tra một số properties cơ bản
        var hasOptions = tableElement._tabulator.options !== null;
        var hasElement = tableElement._tabulator.element !== null;
        
        console.log(`checkTabulatorHealth: ${xmlType} - Options: ${hasOptions}, Element: ${hasElement}`);
        return hasOptions && hasElement;
    } catch (error) {
        console.error(`checkTabulatorHealth: Error checking ${xmlType}:`, error);
        return false;
    }
}

// setupTablesConfigOnly function removed - using setupTables instead

// Hàm khởi tạo chỉ cấu hình bảng (không load dữ liệu)
async function initializeTablesConfigOnly() {
    window.initialPageLoadComplete = false;
    window.allowInitialTabulatorLoad = false; // Ngăn load dữ liệu tự động

    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];

    for (const xmlType of xmlTypes) {
        try {
            // Khởi tạo với setupTables (không load dữ liệu)
            await setupTables(xmlType, false);
            await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
            console.error(`initializeTablesConfigOnly: Error initializing ${xmlType}:`, error);
        }
    }

    window.initialPageLoadComplete = true;
}

// Hàm khởi tạo tất cả các bảng khi load trang (với dữ liệu)
async function initializeAllTables() {
    console.log("initializeAllTables: Initializing all Tabulator instances...");
    window.initialPageLoadComplete = false; // Đặt lại cờ này khi bắt đầu khởi tạo

    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];

    for (const xmlType of xmlTypes) {
        try {
            await setupTables(xmlType, false);
            // Đợi một chút giữa các lần khởi tạo
            await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
            console.error(`initializeAllTables: Error initializing ${xmlType}:`, error);
        }
    }

    console.log("initializeAllTables: Completed initializing all tables");
    window.initialPageLoadComplete = true; // Đánh dấu đã hoàn tất khởi tạo ban đầu

    // Đăng ký event listeners cho các nút tải dữ liệu
    registerDataLoadButtons();
}

// Hàm đăng ký các nút tải dữ liệu
function registerDataLoadButtons() {
    // Đăng ký nút tải dữ liệu chính
    const loadDataBtn = document.getElementById('load-all-data-btn');
    if (loadDataBtn) {
        loadDataBtn.addEventListener('click', async function() {
            try {
                this.disabled = true;
                this.textContent = 'Đang tải...';
                
                await loadAllXMLData();
                
            } catch (error) {
                console.error('Error loading data:', error);
                if (typeof showNotification === 'function') {
                    showNotification('Lỗi tải dữ liệu: ' + error.message, 'error');
                }
            } finally {
                this.disabled = false;
                this.textContent = 'Tải dữ liệu';
            }
        });
    }
    
    // Đăng ký nút reload dữ liệu
    const reloadDataBtn = document.getElementById('reload-all-data-btn');
    if (reloadDataBtn) {
        reloadDataBtn.addEventListener('click', async function() {
            try {
                this.disabled = true;
                this.textContent = 'Đang tải lại...';
                
                await reloadAllTableData();
                
            } catch (error) {
                console.error('Error reloading data:', error);
                if (typeof showNotification === 'function') {
                    showNotification('Lỗi tải lại dữ liệu: ' + error.message, 'error');
                }
            } finally {
                this.disabled = false;
                this.textContent = 'Tải lại dữ liệu';
            }
        });
    }
    
    // Đăng ký nút clear tất cả dữ liệu
    const clearDataBtn = document.getElementById('clear-all-data-btn');
    if (clearDataBtn) {
        clearDataBtn.addEventListener('click', function() {
            if (confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu trong các bảng?')) {
                clearAllTableData();
            }
        });
    }
    
    // Đăng ký các nút tải dữ liệu riêng lẻ cho từng XML type
    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    xmlTypes.forEach(xmlType => {
        const loadBtn = document.getElementById(`load-${xmlType.toLowerCase()}-data-btn`);
        if (loadBtn) {
            loadBtn.addEventListener('click', async function() {
                try {
                    this.disabled = true;
                    this.textContent = `Đang tải ${xmlType}...`;
                    
                    if (xmlType === 'XML0' || xmlType === 'XML1' || xmlType === 'XML12') {
                        // Nếu là XML0, XML1, XML12 (không phụ thuộc maLK từ XML1), load trực tiếp
                        const data = await fetchXMLData(xmlType, window.currentGlobalFilters);
                        await loadDataIntoTable(xmlType, data || []);
                    } else {
                        // Nếu là XML khác, cần có maLK từ XML1
                        const xml1Table = document.getElementById('xml1-table');
                        if (xml1Table && xml1Table._tabulator) {
                            const xml1Data = xml1Table._tabulator.getData();
                            const maLKList = xml1Data.map(item => item.maLK).filter(maLK => maLK);
                            
                            if (maLKList.length > 0) {
                                const data = await fetchXMLData(xmlType, { ...window.currentGlobalFilters, maLK__in: maLKList.join(',') });
                                await loadDataIntoTable(xmlType, data || []);
                            } else {
                                if (typeof showNotification === 'function') {
                                    showNotification('Không có dữ liệu XML1 để lấy maLK. Vui lòng tải XML1 trước.', 'warning');
                                }
                            }
                        } else {
                            if (typeof showNotification === 'function') {
                                showNotification('Bảng XML1 chưa được khởi tạo. Vui lòng tải XML1 trước.', 'warning');
                            }
                        }
                    }
                    
                } catch (error) {
                    console.error(`Error loading ${xmlType} data:`, error);
                    if (typeof showNotification === 'function') {
                        showNotification(`Lỗi tải dữ liệu ${xmlType}: ` + error.message, 'error');
                    }
                } finally {
                    this.disabled = false;
                    this.textContent = `Tải ${xmlType}`;
                }
            });
        }
    });
    
    console.log("registerDataLoadButtons: Registered all data load button event listeners");
}

// Hàm clear tất cả dữ liệu trong các bảng
function clearAllTableData() {
    const xmlTypes = ['XML0', 'XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    xmlTypes.forEach(xmlType => {
        const tableElementId = xmlType.toLowerCase() + '-table';
        const tableElement = document.getElementById(tableElementId);
        
        if (tableElement && tableElement._tabulator) {
            try {
                tableElement._tabulator.setData([]);
            } catch (error) {
                console.error(`clearAllTableData: Error clearing ${xmlType}:`, error);
            }
        }
    });
    
    if (typeof showNotification === 'function') {
        showNotification('Đã xóa tất cả dữ liệu trong các bảng', 'success');
    }
}



// Hàm lấy dữ liệu đã được filter từ một bảng cụ thể
function getFilteredTableData(xmlType) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            return tableElement._tabulator.getData("active");
        } catch (error) {
            console.error(`getFilteredTableData: Error getting filtered data from ${xmlType}:`, error);
            return [];
        }
    }
    
    console.warn(`getFilteredTableData: No Tabulator instance found for ${xmlType}`);
    return [];
}

// Hàm lấy dữ liệu đã được chọn từ một bảng cụ thể
function getSelectedTableData(xmlType) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const selectedRows = tableElement._tabulator.getSelectedRows();
            return selectedRows.map(row => row.getData());
        } catch (error) {
            console.error(`getSelectedTableData: Error getting selected data from ${xmlType}:`, error);
            return [];
        }
    }
    console.warn(`getSelectedTableData: No Tabulator instance found for ${xmlType}`);
    return [];
}

// Hàm cập nhật một dòng dữ liệu trong bảng
function updateTableRow(xmlType, maLK, newData) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const row = tableElement._tabulator.getRow(maLK);
            if (row) {
                row.update(newData);
                console.log(`updateTableRow: Updated row ${maLK} in ${xmlType}`);
                return true;
            } else {
                console.warn(`updateTableRow: Row ${maLK} not found in ${xmlType}`);
                return false;
            }
        } catch (error) {
            console.error(`updateTableRow: Error updating row ${maLK} in ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`updateTableRow: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm thêm một dòng mới vào bảng
function addTableRow(xmlType, newData, position = "bottom") {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            tableElement._tabulator.addRow(newData, position === "top");
            console.log(`addTableRow: Added new row to ${xmlType}`);
            return true;
        } catch (error) {
            console.error(`addTableRow: Error adding row to ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`addTableRow: No Tabulator instance found for ${xmlType}`);
    return false;
}



// Hàm export dữ liệu từ bảng
function exportTableData(xmlType, format = "xlsx", filename = null) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const exportFilename = filename || `${xmlType}_data_${new Date().toISOString().split('T')[0]}`;
            
            switch (format.toLowerCase()) {
                case 'xlsx':
                    tableElement._tabulator.download("xlsx", `${exportFilename}.xlsx`, {sheetName: xmlType});
                    break;
                case 'csv':
                    tableElement._tabulator.download("csv", `${exportFilename}.csv`);
                    break;
                case 'json':
                    tableElement._tabulator.download("json", `${exportFilename}.json`);
                    break;
                case 'pdf':
                    tableElement._tabulator.download("pdf", `${exportFilename}.pdf`, {
                        orientation: "landscape",
                        title: `${xmlType} Data Export`
                    });
                    break;
                default:
                    console.warn(`exportTableData: Unsupported format ${format}`);
                    return false;
            }
            
            console.log(`exportTableData: Exported ${xmlType} data as ${format}`);
            return true;
        } catch (error) {
            console.error(`exportTableData: Error exporting ${xmlType} data:`, error);
            return false;
        }
    }
    
    console.warn(`exportTableData: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm reset filters cho một bảng
function resetTableFilters(xmlType) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            tableElement._tabulator.clearFilter();
            tableElement._tabulator.clearHeaderFilter();
            console.log(`resetTableFilters: Reset filters for ${xmlType}`);
            return true;
        } catch (error) {
            console.error(`resetTableFilters: Error resetting filters for ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`resetTableFilters: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm reset tất cả filters cho tất cả bảng
function resetAllTableFilters() {
    const xmlTypes = ['XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    let resetCount = 0;
    xmlTypes.forEach(xmlType => {
        if (resetTableFilters(xmlType)) {
            resetCount++;
        }
    });
    
    if (typeof showNotification === 'function') {
        showNotification(`Đã reset filters cho ${resetCount} bảng`, 'success');
    }
    
    console.log(`resetAllTableFilters: Reset filters for ${resetCount} tables`);
}

// Hàm lấy thống kê về dữ liệu trong bảng
function getTableStats(xmlType) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const allData = tableElement._tabulator.getData();
            const filteredData = tableElement._tabulator.getData("active");
            const selectedData = tableElement._tabulator.getSelectedRows().map(row => row.getData());
            
            return {
                total: allData.length,
                filtered: filteredData.length,
                selected: selectedData.length,
                xmlType: xmlType
            };
        } catch (error) {
            console.error(`getTableStats: Error getting stats for ${xmlType}:`, error);
            return {
                total: 0,
                filtered: 0,
                selected: 0,
                xmlType: xmlType,
                error: error.message
            };
        }
    }
    
    console.warn(`getTableStats: No Tabulator instance found for ${xmlType}`);
    return {
        total: 0,
        filtered: 0,
        selected: 0,
        xmlType: xmlType,
        error: "No Tabulator instance found"
    };
}

// Hàm lấy thống kê cho tất cả bảng
function getAllTableStats() {
    const xmlTypes = ['XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    const stats = {};
    xmlTypes.forEach(xmlType => {
        stats[xmlType] = getTableStats(xmlType);
    });
    
    return stats;
}

// Hàm refresh một bảng cụ thể
function refreshTable(xmlType) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            tableElement._tabulator.redraw();
            console.log(`refreshTable: Refreshed ${xmlType} table`);
            return true;
        } catch (error) {
            console.error(`refreshTable: Error refreshing ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`refreshTable: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm refresh tất cả bảng
function refreshAllTables() {
    const xmlTypes = ['XML1', 'XML2', 'XML3', 'XML4', 'XML5', 'XML6', 'XML7', 'XML8', 'XML9', 'XML10', 'XML11', 'XML12', 'XML13', 'XML14', 'XML15'];
    
    let refreshCount = 0;
    xmlTypes.forEach(xmlType => {
        if (refreshTable(xmlType)) {
            refreshCount++;
        }
    });
    
    console.log(`refreshAllTables: Refreshed ${refreshCount} tables`);
    return refreshCount;
}

// Hàm kiểm tra xem một bảng có dữ liệu không
function hasTableData(xmlType) {
    const data = getTableData(xmlType);
    return data && data.length > 0;
}

// Hàm tìm kiếm dữ liệu trong bảng theo maLK
function findRowByMaLK(xmlType, maLK) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const row = tableElement._tabulator.getRow(maLK);
            return row ? row.getData() : null;
        } catch (error) {
            console.error(`findRowByMaLK: Error finding row ${maLK} in ${xmlType}:`, error);
            return null;
        }
    }
    
    console.warn(`findRowByMaLK: No Tabulator instance found for ${xmlType}`);
    return null;
}

// Hàm scroll đến một dòng cụ thể
function scrollToRow(xmlType, maLK) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const row = tableElement._tabulator.getRow(maLK);
            if (row) {
                tableElement._tabulator.scrollToRow(maLK, "center", false);
                console.log(`scrollToRow: Scrolled to row ${maLK} in ${xmlType}`);
                return true;
            } else {
                console.warn(`scrollToRow: Row ${maLK} not found in ${xmlType}`);
                return false;
            }
        } catch (error) {
            console.error(`scrollToRow: Error scrolling to row ${maLK} in ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`scrollToRow: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm highlight một dòng cụ thể
function highlightRow(xmlType, maLK, duration = 3000) {
    const tableElementId = xmlType.toLowerCase() + '-table';
    const tableElement = document.getElementById(tableElementId);
    
    if (tableElement && tableElement._tabulator) {
        try {
            const row = tableElement._tabulator.getRow(maLK);
            if (row) {
                // Thêm class highlight
                row.getElement().classList.add('highlighted-row');
                
                // Tự động remove highlight sau duration
                setTimeout(() => {
                    if (row.getElement()) {
                        row.getElement().classList.remove('highlighted-row');
                    }
                }, duration);
                
                console.log(`highlightRow: Highlighted row ${maLK} in ${xmlType}`);
                return true;
            } else {
                console.warn(`highlightRow: Row ${maLK} not found in ${xmlType}`);
                return false;
            }
        } catch (error) {
            console.error(`highlightRow: Error highlighting row ${maLK} in ${xmlType}:`, error);
            return false;
        }
    }
    
    console.warn(`highlightRow: No Tabulator instance found for ${xmlType}`);
    return false;
}

// Hàm validate dữ liệu trong bảng
function validateTableData(xmlType, providedData = null) {
    // Sử dụng dữ liệu được cung cấp hoặc lấy từ table
    const data = providedData || getTableData(xmlType);
    const errors = [];

    if (!data || !Array.isArray(data) || data.length === 0) {
        return { isValid: true, errors: [], message: "No data to validate" };
    }

    data.forEach((row, index) => {
        // Kiểm tra maLK bắt buộc
        if (!row.maLK || row.maLK.trim() === '') {
            errors.push({
                row: index + 1,
                field: 'maLK',
                message: 'Mã liên kết không được để trống'
            });
        }
        
        // Thêm các validation rules khác tùy theo xmlType
        switch (xmlType) {
            case 'XML1':
                if (!row.hoTen || row.hoTen.trim() === '') {
                    errors.push({
                        row: index + 1,
                        field: 'hoTen',
                        message: 'Họ tên không được để trống'
                    });
                }
                break;
            case 'XML2':
                if (!row.maThuoc || row.maThuoc.trim() === '') {
                    errors.push({
                        row: index + 1,
                        field: 'maThuoc',
                        message: 'Mã thuốc không được để trống'
                    });
                }
                break;
            // Thêm validation cho các XML type khác nếu cần
        }
    });
    
    return {
        isValid: errors.length === 0,
        errors: errors,
        message: errors.length === 0 ? "Dữ liệu hợp lệ" : `Tìm thấy ${errors.length} lỗi`
    };
}

// Export các hàm để sử dụng ở nơi khác
window.TabulatorXMLTables = {
    // Core functions
    setupTables,
    initializeAllTables,
    initializeTablesConfigOnly,
    loadAllXMLData,
    loadDataIntoTable,
    reloadAllTableData,
    
    // Data access and manipulation functions
    getTableData,
    getFilteredTableData,
    getSelectedTableData,
    updateTableRow,
    addTableRow,
    deleteTableRow,
    clearAllTableData,
    // Utility functions
    exportTableData,
    resetTableFilters,
    resetAllTableFilters,
    getTableStats,
    getAllTableStats,
    refreshTable,
    refreshAllTables,
    hasTableData,
    findRowByMaLK,
    scrollToRow,
    highlightRow,
    validateTableData,
    // Helper functions
    switchPaginationMode,
    checkTabulatorHealth,
    showLoadingForAllTables,
    hideLoadingForAllTables,
    registerDataLoadButtons
};

// ===== GLOBAL EXPORTS =====
if (typeof window !== 'undefined') {
    // Initialize the global object if it doesn't exist
    window.TabulatorXMLTables = window.TabulatorXMLTables || {};
    
    // ===== MAIN FUNCTIONS =====
    window.TabulatorXMLTables.loadAllXMLData = loadAllXMLData;
    window.TabulatorXMLTables.loadTableData = loadTableData;
    window.TabulatorXMLTables.loadDataIntoTable = loadDataIntoTable;
    window.TabulatorXMLTables.switchPaginationMode = switchPaginationMode;
    window.TabulatorXMLTables.refreshTable = refreshTable;
    
    // ===== HELPER FUNCTIONS =====
    window.TabulatorXMLTables.getTableData = getTableData;
    window.TabulatorXMLTables.clearTableData = clearTableData;
    window.TabulatorXMLTables.clearAllTableData = clearAllTableData;
    window.TabulatorXMLTables.deleteTableRow = deleteTableRow;
    window.TabulatorXMLTables.showLoadingForAllTables = showLoadingForAllTables;
} else {
    console.warn("⚠️ Window object not available - functions not exported");
}