# Generated by Django 4.2.7 on 2025-07-06 06:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('xml4750', '0021_xml9fieldconfig_xml8fieldconfig_xml7fieldconfig_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='xml0fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml10fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml11fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml12fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml13fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml14fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml15fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml1fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml2fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml3fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml4fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml5fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml6fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml7fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml8fieldconfig',
            name='format',
        ),
        migrations.RemoveField(
            model_name='xml9fieldconfig',
            name='format',
        ),
        migrations.AddField(
            model_name='validationrule',
            name='compare_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Giá trị so sánh'),
        ),
        migrations.AddField(
            model_name='xml0fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml0fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml0fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml10fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml10fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml10fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml11fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml11fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml11fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml12fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml12fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml12fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml13fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml13fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml13fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml14fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml14fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml14fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml15fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml15fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml15fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml1fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml1fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml1fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml2fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml2fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml2fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml3fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml3fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml3fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml4fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml4fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml4fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml5fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml5fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml5fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml6fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml6fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml6fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml7fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml7fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml7fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml8fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml8fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml8fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AddField(
            model_name='xml9fieldconfig',
            name='compare_type',
            field=models.CharField(choices=[('exact', 'Chính xác'), ('length', 'Độ dài'), ('range', 'Khoảng giá trị'), ('pattern', 'Mẫu regex'), ('list', 'Danh sách cho phép'), ('skip', 'Bỏ qua kiểm tra')], default='exact', max_length=20, verbose_name='Kiểu so sánh'),
        ),
        migrations.AddField(
            model_name='xml9fieldconfig',
            name='size_value',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Kích thước/Giá trị'),
        ),
        migrations.AddField(
            model_name='xml9fieldconfig',
            name='warning',
            field=models.BooleanField(default=False, verbose_name='Chỉ cảnh báo'),
        ),
        migrations.AlterField(
            model_name='validationrule',
            name='condition',
            field=models.CharField(choices=[('equals', 'Bằng'), ('not_equals', 'Không bằng'), ('greater_than', 'Lớn hơn'), ('greater_equal', 'Lớn hơn hoặc bằng'), ('less_than', 'Nhỏ hơn'), ('less_equal', 'Nhỏ hơn hoặc bằng'), ('contains', 'Chứa'), ('not_contains', 'Không chứa'), ('starts_with', 'Bắt đầu bằng'), ('ends_with', 'Kết thúc bằng'), ('regex', 'Khớp với regex'), ('in_list', 'Trong danh sách'), ('not_in_list', 'Không trong danh sách'), ('custom', 'Tùy chỉnh')], max_length=20, verbose_name='Điều kiện'),
        ),
        migrations.AlterField(
            model_name='validationrule',
            name='target_field',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='Trường đích'),
        ),
        migrations.AlterField(
            model_name='validationrule',
            name='target_xml_type',
            field=models.CharField(blank=True, max_length=10, null=True, verbose_name='Loại XML đích'),
        ),
    ]
