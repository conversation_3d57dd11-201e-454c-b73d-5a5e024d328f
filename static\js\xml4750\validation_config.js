/**
 * XML4750 Validation Configuration Module
 * Quản lý cấu hình kiểm tra dữ liệu XML
 */

// Namespace cho module validation config
window.XMLValidationConfig = (function() {
    
    // Lưu trữ cấu hình
    let fieldConfigs = {};
    let validationRules = [];
    
    /**
     * Tải tất cả cấu hình từ server
     * @returns {Promise} Promise khi tải xong
     */
    function loadAllConfigs() {
        return Promise.all([
            loadFieldConfigs(),
            loadValidationRules()
        ]);
    }
    
    /**
     * Tải cấu hình trường từ server
     * @returns {Promise} Promise khi tải xong
     */
    function loadFieldConfigs() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/xml4750/api/get_all_field_configs/',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        fieldConfigs = response.configs;
                        console.log('Loaded field configs:', fieldConfigs);
                        resolve(fieldConfigs);
                    } else {
                        reject(new Error(response.message || '<PERSON>hông thể tải cấu hình trường'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading field configs:', error);
                    reject(new Error('Lỗi kết nối khi tải cấu hình trường'));
                }
            });
        });
    }
    
    /**
     * Tải quy tắc kiểm tra từ server
     * @returns {Promise} Promise khi tải xong
     */
    function loadValidationRules() {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/xml4750/api/get_validation_rules/',
                type: 'GET',
                success: function(response) {
                    if (response.success) {
                        validationRules = response.rules;
                        console.log('Loaded validation rules:', validationRules);
                        resolve(validationRules);
                    } else {
                        reject(new Error(response.message || 'Không thể tải quy tắc kiểm tra'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading validation rules:', error);
                    reject(new Error('Lỗi kết nối khi tải quy tắc kiểm tra'));
                }
            });
        });
    }
    
    /**
     * Lưu cấu hình trường
     * @param {Array} configs - Danh sách cấu hình trường
     * @returns {Promise} Promise khi lưu xong
     */
    function saveFieldConfigs(configs) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/xml4750/api/save_field_config/',
                type: 'POST',
                data: {
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val(),
                    'configs': JSON.stringify(configs)
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response);
                    } else {
                        reject(new Error(response.message || 'Không thể lưu cấu hình trường'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error saving field configs:', error);
                    reject(new Error('Lỗi kết nối khi lưu cấu hình trường'));
                }
            });
        });
    }
    
    /**
     * Lưu quy tắc kiểm tra
     * @param {Array} rules - Danh sách quy tắc kiểm tra
     * @returns {Promise} Promise khi lưu xong
     */
    function saveValidationRules(rules) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/xml4750/api/save_validation_rules/',
                type: 'POST',
                data: {
                    'csrfmiddlewaretoken': $('[name=csrfmiddlewaretoken]').val(),
                    'rules': JSON.stringify(rules)
                },
                success: function(response) {
                    if (response.success) {
                        validationRules = rules;
                        resolve(response);
                    } else {
                        reject(new Error(response.message || 'Không thể lưu quy tắc kiểm tra'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error saving validation rules:', error);
                    reject(new Error('Lỗi kết nối khi lưu quy tắc kiểm tra'));
                }
            });
        });
    }
    
    /**
     * Lấy cấu hình của một trường cụ thể
     * @param {string} xmlType - Loại XML
     * @param {string} fieldName - Tên trường
     * @returns {Object|null} Cấu hình trường hoặc null nếu không có
     */
    function getFieldConfig(xmlType, fieldName) {
        const configKey = `${xmlType}.${fieldName}`;
        return fieldConfigs[configKey] || null;
    }
    
    /**
     * Lấy tất cả quy tắc kiểm tra
     * @returns {Array} Danh sách quy tắc kiểm tra
     */
    function getValidationRules() {
        return validationRules;
    }
    
    /**
     * Lấy danh sách trường của một loại XML
     * @param {string} xmlType - Loại XML
     * @returns {Promise} Promise với danh sách trường
     */
    function getXmlFields(xmlType) {
        return new Promise((resolve, reject) => {
            $.ajax({
                url: '/xml4750/api/get_xml_fields/',
                type: 'GET',
                data: {
                    xml_type: xmlType
                },
                success: function(response) {
                    if (response.success) {
                        resolve(response.fields);
                    } else {
                        reject(new Error(response.message || 'Không thể tải danh sách trường'));
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading XML fields:', error);
                    reject(new Error('Lỗi kết nối khi tải danh sách trường'));
                }
            });
        });
    }
    
    /**
     * Kiểm tra một giá trị theo cấu hình trường
     * @param {string} xmlType - Loại XML
     * @param {string} fieldName - Tên trường
     * @param {any} value - Giá trị cần kiểm tra
     * @returns {Object} Kết quả kiểm tra {valid: boolean, message: string, blocking: boolean}
     */
    function validateFieldValue(xmlType, fieldName, value) {
        const config = getFieldConfig(xmlType, fieldName);
        
        if (!config) {
            return { valid: true, message: null, blocking: false };
        }
        
        // Kiểm tra trường bắt buộc
        if (config.required && (value === null || value === undefined || value === '')) {
            return {
                valid: false,
                message: `Trường ${fieldName} là bắt buộc`,
                blocking: config.blocking
            };
        }
        
        // Kiểm tra kiểu dữ liệu
        if (value !== null && value !== undefined && value !== '') {
            if (config.dataType === 'number' && isNaN(Number(value))) {
                return {
                    valid: false,
                    message: `Trường ${fieldName} phải là số`,
                    blocking: config.blocking
                };
            }
            
            if (config.dataType === 'date' && config.format) {
                // Kiểm tra định dạng ngày
                const dateRegex = new RegExp(config.format);
                if (!dateRegex.test(value)) {
                    return {
                        valid: false,
                        message: `Trường ${fieldName} không đúng định dạng ${config.format}`,
                        blocking: config.blocking
                    };
                }
            }
        }
        
        return { valid: true, message: null, blocking: false };
    }
    
    // Public API
    return {
        loadAllConfigs: loadAllConfigs,
        loadFieldConfigs: loadFieldConfigs,
        loadValidationRules: loadValidationRules,
        saveFieldConfigs: saveFieldConfigs,
        saveValidationRules: saveValidationRules,
        getFieldConfig: getFieldConfig,
        getValidationRules: getValidationRules,
        getXmlFields: getXmlFields,
        validateFieldValue: validateFieldValue
    };
})();
